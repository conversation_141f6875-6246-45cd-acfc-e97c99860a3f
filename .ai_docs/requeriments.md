Check the docs in AI docs and create a dashboard for customer service where we can aggregate potencial patients to send whatsapp messages and focus our campaing

Create a MVP with a Few Metrics and we will expand in the future

Use: 
Next.js + Tailwind CSS + shadcn/ui
This is essentially one integrated solution that gives you everything:
Next.js - Handles routing, API routes, deployment, and React all in one framework with excellent documentation
Tailwind CSS 
shadcn/ui 
For Charts: Recharts

Design Details

Clean white background with subtle gray card borders
Blue color scheme (#3B82F6 or similar)
Rounded corners on all cards
Proper spacing and typography
Charts should be interactive/responsive
