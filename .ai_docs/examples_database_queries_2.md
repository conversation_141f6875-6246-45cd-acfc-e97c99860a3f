A seguir as mesmas 10 queries, ajustadas ao padrão SQL do IRIS (schema `dado`), com:

* uso de `CURRENT_TIMESTAMP` ou `CURRENT_DATE` em vez de `GETDATE()`,
* sintaxe `DATEADD('day',…)` e `DATEDIFF('day',…)`,
* qualificação completa de tabelas,
* correções de `GROUP BY` e de agregações condicionais.

Você pode parametrizar datas (por ex. `:periodo_dias`, `:data_inicial`, `:data_final`) conforme sua ferramenta de BI.

---

🎯 **TOP 10 KPIs PRINCIPAIS**

1. **Taxa de Recorrência por Período**
   Pacientes que retornaram em até N dias (30/60/90, etc).

   ```sql
   WITH pacientes_periodo AS (
     SELECT
       p.ID,
       DATEDIFF('day', p.UltimaOS, CURRENT_TIMESTAMP) AS dias_ultima_os
     FROM dado.ArqPaciente p
     WHERE p.UltimaOS >= DATEADD('day', -:periodo_dias, CURRENT_TIMESTAMP)
   )
   SELECT
     SUM(CASE WHEN dias_ultima_os <= :periodo_dias THEN 1 ELSE 0 END) * 100.0
       / COUNT(*) AS taxa_recorrencia_perc
   FROM pacientes_periodo;
   ```

2. **Customer Lifetime Value (CLV)**
   Valor médio de OS por paciente, anualizado.

   ```sql
   SELECT
     p.ID,
     p.TotalOS,
     DATEDIFF('day', p.PrimeiraOS, p.UltimaOS) AS dias_relacionamento,
     (p.TotalOS * 1.0)
       / NULLIF(DATEDIFF('day', p.PrimeiraOS, p.UltimaOS), 0) * 365
       AS frequencia_anual_estimada
   FROM dado.ArqPaciente p
   WHERE p.TotalOS > 1;
   ```

3. **Score RFM (Recência, Frequência, Monetário)**
   Segmentação em quintis.

   ```sql
   WITH rfm AS (
     SELECT
       p.ID,
       DATEDIFF('day', p.UltimaOS, CURRENT_TIMESTAMP) AS recencia,
       p.TotalOS AS frequencia,
       COUNT(os.ID) AS valor_monetario
     FROM dado.ArqPaciente p
     LEFT JOIN dado.ArqOrdemServico os
       ON os.Paciente = p.ID
     GROUP BY p.ID, p.UltimaOS, p.TotalOS
   )
   SELECT
     ID,
     NTILE(5) OVER (ORDER BY recencia)            AS score_recencia,
     NTILE(5) OVER (ORDER BY frequencia DESC)     AS score_frequencia,
     NTILE(5) OVER (ORDER BY valor_monetario DESC) AS score_monetario
   FROM rfm;
   ```

4. **Taxa de Churn por Cohort**
   Pacientes em risco conforme dias desde última OS.

   ```sql
   SELECT
     CASE
       WHEN DATEDIFF('day', p.UltimaOS, CURRENT_TIMESTAMP) > 365 THEN 'Churn Alto Risco'
       WHEN DATEDIFF('day', p.UltimaOS, CURRENT_TIMESTAMP) > 180 THEN 'Churn Médio Risco'
       WHEN DATEDIFF('day', p.UltimaOS, CURRENT_TIMESTAMP) >  90 THEN 'Churn Baixo Risco'
       ELSE 'Ativo'
     END AS status_churn,
     COUNT(*) AS qtd_pacientes
   FROM dado.ArqPaciente p
   GROUP BY
     CASE
       WHEN DATEDIFF('day', p.UltimaOS, CURRENT_TIMESTAMP) > 365 THEN 'Churn Alto Risco'
       WHEN DATEDIFF('day', p.UltimaOS, CURRENT_TIMESTAMP) > 180 THEN 'Churn Médio Risco'
       WHEN DATEDIFF('day', p.UltimaOS, CURRENT_TIMESTAMP) >  90 THEN 'Churn Baixo Risco'
       ELSE 'Ativo'
     END;
   ```

5. **Performance por Posto/Unidade**
   Produtividade e fidelização.

   ```sql
   SELECT
     os.Posto,
     COUNT(DISTINCT os.Paciente)                  AS pacientes_unicos,
     COUNT(*)                                    AS total_exames,
     COUNT(*) * 1.0 / COUNT(DISTINCT os.Paciente) AS exames_por_paciente,
     SUM(CASE WHEN p.TotalOS > 1 THEN 1 ELSE 0 END) * 100.0
       / COUNT(DISTINCT os.Paciente)             AS taxa_fidelizacao_perc
   FROM dado.ArqOrdemServico os
   JOIN dado.ArqPaciente p
     ON os.Paciente = p.ID
   WHERE os.OsStatus = 1
   GROUP BY os.Posto;
   ```

---

📈 **MÉTRICAS DE FIDELIZAÇÃO**

6. **Tempo Médio Entre Exames**
   Média e mediana dos intervalos.

   ```sql
   WITH intervalos AS (
     SELECT
       os.Paciente,
       os.Data,
       LAG(os.Data) OVER (
         PARTITION BY os.Paciente
         ORDER BY os.Data
       ) AS data_anterior,
       DATEDIFF(
         'day',
         LAG(os.Data) OVER (PARTITION BY os.Paciente ORDER BY os.Data),
         os.Data
       ) AS dias_intervalo
     FROM dado.ArqOrdemServico os
     WHERE os.OsStatus = 1
   )
   SELECT
     AVG(dias_intervalo) AS intervalo_medio_dias,
     PERCENTILE_CONT(0.5)
       WITHIN GROUP (ORDER BY dias_intervalo) AS mediana_intervalo
   FROM intervalos
   WHERE dias_intervalo IS NOT NULL;
   ```

7. **Taxa de Cross-Sell por Categoria**
   Pacientes com múltiplas categorias de exame.

   ```sql
   SELECT
     os.Paciente,
     COUNT(DISTINCT os.Categoria) AS categorias_diferentes,
     CASE
       WHEN COUNT(DISTINCT os.Categoria) = 1 THEN 'Monotipo'
       WHEN COUNT(DISTINCT os.Categoria) <= 3 THEN 'Baixa Variedade'
       ELSE 'Alta Variedade'
     END AS perfil_cross_sell
   FROM dado.ArqOrdemServico os
   WHERE os.OsStatus = 1
   GROUP BY os.Paciente;
   ```

---

🎯 **SEGMENTAÇÃO ESTRATÉGICA**

8. **Perfil Demográfico vs Comportamento**
   Faixa etária × média de exames.

   ```sql
   SELECT
     pf.Sexo,
     CASE
       WHEN DATEDIFF('year', pf.DataNascimento, CURRENT_DATE) < 30 THEN 'Jovem'
       WHEN DATEDIFF('year', pf.DataNascimento, CURRENT_DATE) < 50 THEN 'Adulto'
       WHEN DATEDIFF('year', pf.DataNascimento, CURRENT_DATE) < 65 THEN 'Meia-idade'
       ELSE 'Idoso'
     END AS faixa_etaria,
     AVG(p.TotalOS) AS media_exames,
     COUNT(*)      AS qtd_pacientes
   FROM dado.TblPessoaFisica pf
   JOIN dado.ArqPaciente p
     ON p.PessoaFisica = pf.ID
   GROUP BY
     pf.Sexo,
     CASE
       WHEN DATEDIFF('year', pf.DataNascimento, CURRENT_DATE) < 30 THEN 'Jovem'
       WHEN DATEDIFF('year', pf.DataNascimento, CURRENT_DATE) < 50 THEN 'Adulto'
       WHEN DATEDIFF('year', pf.DataNascimento, CURRENT_DATE) < 65 THEN 'Meia-idade'
       ELSE 'Idoso'
     END;
   ```

9. **Pacientes VIP e Alto Valor**
   Identificação de segmentos.

   ```sql
   SELECT
     p.ID,
     pf.Nome,
     p.TotalOS,
     pf.Vip,
     CASE
       WHEN p.TotalOS >= 10 THEN 'Alto Valor'
       WHEN p.TotalOS >=  5 THEN 'Médio Valor'
       ELSE 'Baixo Valor'
     END AS segmento_valor
   FROM dado.ArqPaciente p
   JOIN dado.TblPessoaFisica pf
     ON p.PessoaFisica = pf.ID
   WHERE p.TotalOS > 0
   ORDER BY p.TotalOS DESC;
   ```

⚠️ **ALERTAS AUTOMÁTICOS**

10. **Pacientes em Risco de Churn**
    Filtra quem não vem há 90-180 dias e permite comunicação.

```sql
SELECT
  pf.Nome,
  pf.Email,
  p.UltimaOS,
  DATEDIFF('day', p.UltimaOS, CURRENT_TIMESTAMP) AS dias_sem_exame,
  p.TotalOS,
  'AÇÃO: Campanha de Reativação'                AS acao_sugerida
FROM dado.ArqPaciente p
JOIN dado.TblPessoaFisica pf
  ON p.PessoaFisica = pf.ID
WHERE DATEDIFF('day', p.UltimaOS, CURRENT_TIMESTAMP) BETWEEN  90 AND 180
  AND p.TotalOS >= 3
  AND pf.PermiteComunicacao = 'S'
ORDER BY p.TotalOS DESC, dias_sem_exame DESC;
```

---

**Próximos passos**

* Substitua `:periodo_dias`, `:data_inicial`/`:data_final` pelos parâmetros do seu BI.
* Ajuste horários (`CURRENT_TIMESTAMP` vs. `CURRENT_DATE`) conforme necessidade.
* Integre essas queries em dashboards para monitorar atendimento, retenção e oportunidades de cross-sell.
