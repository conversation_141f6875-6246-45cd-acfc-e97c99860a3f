Abaixo estão duas listas de métricas (Customer Service e Customer Analytics) pensadas para um laboratório de análises clínicas, seguidas das queries SQL prontas para você rodar no namespace `dado` do IRIS. Em todas as consultas de “volume” você pode acrescentar um filtro de período, por exemplo:

```sql
WHERE OS.Data BETWEEN '2025-01-01' AND '2025-06-26'
```

…ou substituir por `:data_inicial` e `:data_final` em sua ferramenta de BI.

---

## 1. Métricas de Customer Service

1. **Volume de Ordens de Serviço**
   Quantidade total de OS num período.

   ```sql
   SELECT 
     COUNT(*) AS TotalOS
   FROM dado.ArqOrdemServico OS
   /*WHERE OS.Data BETWEEN :data_inicial AND :data_final*/;
   ```

2. **Tempo Médio de Atendimento (em minutos)**
   Média de `HoraFinal - HoraInicial`.

   ```sql
   SELECT
     AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) 
       AS TempoMedioAtendimentoMin
   FROM dado.ArqOrdemServico OS;
   ```

3. **Taxa de Reimpressão de Senhas**
   % de OS com reimpressão (campo `ReimpressaoData` não nulo).

   ```sql
   SELECT
     COUNT(*) * 100.0 / (SELECT COUNT(*) FROM dado.ArqOrdemServico)
       AS TaxaReimpressaoPerc
   FROM dado.ArqOrdemServico OS
   WHERE OS.ReimpressaoData IS NOT NULL;
   ```

4. **Distribuição de OS por Tipo de Atendimento**
   Quantidade e % por `TipoAtendimento`.

   ```sql
   SELECT
     OS.TipoAtendimento,
     COUNT(*)               AS QtdeOS,
     COUNT(*) * 100.0 / SUM(COUNT(*)) OVER()
       AS PercOS
   FROM dado.ArqOrdemServico OS
   GROUP BY OS.TipoAtendimento
   ORDER BY QtdeOS DESC;
   ```

5. **Volume Online vs. Presencial**
   Comparativo de `DisponivelWeb` = 1 (agendadas online) vs. 0.

   ```sql
   SELECT
     OS.DisponivelWeb       AS AgendamentoOnline,
     COUNT(*)               AS QtdeOS,
     COUNT(*) * 100.0 / SUM(COUNT(*)) OVER()
       AS PercOS
   FROM dado.ArqOrdemServico OS
   GROUP BY OS.DisponivelWeb;
   ```

6. **OS por Recepcionista**
   Carga de trabalho de cada atendente.

   ```sql
   SELECT
     OS.Recepcionista       AS ID_Recepcionista,
     COUNT(*)               AS QtdeOS
   FROM dado.ArqOrdemServico OS
   GROUP BY OS.Recepcionista
   ORDER BY QtdeOS DESC;
   ```

7. **OS por Segmento de Cliente**
   Ex.: convênios, particular, etc (`Segmento`).

   ```sql
   SELECT
     OS.Segmento,
     COUNT(*)               AS QtdeOS
   FROM dado.ArqOrdemServico OS
   GROUP BY OS.Segmento;
   ```

8. **Taxa de Reagendamento**
   % de OS que foram agendadas (campo `Agendamento` não nulo).

   ```sql
   SELECT
     COUNT(*) * 100.0 / (SELECT COUNT(*) FROM dado.ArqOrdemServico)
       AS TaxaReagendamentoPerc
   FROM dado.ArqOrdemServico OS
   WHERE OS.Agendamento IS NOT NULL;
   ```

9. **Índice de Cancelamento / Não Comparecimento**
   % de OS com `OsStatus = 0` (assumindo que 0 = cancelada).

   ```sql
   SELECT
     COUNT(*) * 100.0 / (SELECT COUNT(*) FROM dado.ArqOrdemServico)
       AS TaxaCancelamentoPerc
   FROM dado.ArqOrdemServico OS
   WHERE OS.OsStatus = 0;
   ```

10. **OS por Dia da Semana**
    Identifica picos de demanda.

    ```sql
    SELECT
      DATEPART('weekday', OS.Data)    AS DiaSemana,
      COUNT(*)                        AS QtdeOS
    FROM dado.ArqOrdemServico OS
    GROUP BY DATEPART('weekday', OS.Data)
    ORDER BY DiaSemana;
    ```

---

## 2. Métricas de Customer Analytics

1. **Pacientes Ativos**
   Qtd. de pacientes distintos com pelo menos 1 OS no período.

   ```sql
   SELECT
     COUNT(DISTINCT OS.Paciente) AS PacientesAtivos
   FROM dado.ArqOrdemServico OS
   /*WHERE OS.Data BETWEEN :data_inicial AND :data_final*/;
   ```

2. **Novos Pacientes no Período**
   Pacientes cuja `PrimeiraOS` cai no intervalo.

   ```sql
   SELECT
     COUNT(*) AS NovosPacientes
   FROM dado.ArqPaciente P
   WHERE P.PrimeiraOS BETWEEN :data_inicial AND :data_final;
   ```

3. **Pacientes Retornantes**
   Qtd. de pacientes com mais de 1 OS no período.

   ```sql
   SELECT
     COUNT(*) AS PacientesRetornantes
   FROM (
     SELECT OS.Paciente, COUNT(*) AS NumOS
     FROM dado.ArqOrdemServico OS
     WHERE OS.Data BETWEEN :data_inicial AND :data_final
     GROUP BY OS.Paciente
     HAVING COUNT(*) > 1
   ) sub;
   ```

4. **Média de OS por Paciente**

   ```sql
   SELECT
     AVG(NumOS) AS MediaOSporPaciente
   FROM (
     SELECT OS.Paciente, COUNT(*) AS NumOS
     FROM dado.ArqOrdemServico OS
     /*WHERE OS.Data BETWEEN :data_inicial AND :data_final*/
     GROUP BY OS.Paciente
   ) sub;
   ```

5. **Intervalo Médio entre OS (dias)**

   ```sql
   SELECT
     AVG(DATEDIFF(
       'day',
       prev.Data,
       cur.Data
     )) AS IntervaloMedioDias
   FROM (
     SELECT
       OS.Paciente,
       OS.Data,
       LAG(OS.Data) OVER (
         PARTITION BY OS.Paciente
         ORDER BY OS.Data
       ) AS PrevData
     FROM dado.ArqOrdemServico OS
     /*WHERE OS.Data BETWEEN :data_inicial AND :data_final*/
   ) t
   WHERE t.PrevData IS NOT NULL;
   ```

6. **Distribuição Etária dos Pacientes**
   Faixas de idade com base em `DataNascimento` de `TblPessoaFisica`.

   ```sql
   SELECT
     CASE
       WHEN DATEDIFF('year', PF.DataNascimento, CURRENT_DATE) < 18 THEN '<18'
       WHEN DATEDIFF('year', PF.DataNascimento, CURRENT_DATE) BETWEEN 18 AND 30 THEN '18-30'
       WHEN DATEDIFF('year', PF.DataNascimento, CURRENT_DATE) BETWEEN 31 AND 50 THEN '31-50'
       ELSE '51+' 
     END AS FaixaIdade,
     COUNT(DISTINCT AP.ID) AS QtdePacientes
   FROM dado.TblPessoaFisica PF
   JOIN dado.ArqPaciente AP
     ON AP.PessoaFisica = PF.ID
   /*JOIN dado.ArqOrdemServico OS
     ON OS.Paciente = AP.ID
    WHERE OS.Data BETWEEN :data_inicial AND :data_final*/
   GROUP BY 1
   ORDER BY FaixaIdade;
   ```

7. **Distribuição por Sexo**

   ```sql
   SELECT
     PF.Sexo,
     COUNT(DISTINCT AP.ID) AS QtdePacientes
   FROM dado.TblPessoaFisica PF
   JOIN dado.ArqPaciente AP
     ON AP.PessoaFisica = PF.ID
   GROUP BY PF.Sexo;
   ```

8. **Distribuição Geográfica (Estado)**

   ```sql
   SELECT
     PF.EnderecoEstado,
     COUNT(DISTINCT AP.ID) AS QtdePacientes
   FROM dado.TblPessoaFisica PF
   JOIN dado.ArqPaciente AP
     ON AP.PessoaFisica = PF.ID
   GROUP BY PF.EnderecoEstado
   ORDER BY QtdePacientes DESC;
   ```

9. **Top 10 Pacientes por Volume de OS**

   ```sql
   SELECT
     AP.ID                     AS PacienteID,
     PF.Cpf                    AS CPF,
     COUNT(*)                  AS QtdeOS
   FROM dado.ArqOrdemServico OS
   JOIN dado.ArqPaciente AP
     ON OS.Paciente = AP.ID
   JOIN dado.TblPessoaFisica PF
     ON AP.PessoaFisica = PF.ID
   GROUP BY AP.ID, PF.Cpf
   ORDER BY QtdeOS DESC
   FETCH FIRST 10 ROWS ONLY;
   ```

10. **Distribuição de Categoria de OS**
    (ex.: tipos de exame ou serviço)

```sql
SELECT
  OS.Categoria,
  COUNT(*) AS QtdeOS
FROM dado.ArqOrdemServico OS
GROUP BY OS.Categoria
ORDER BY QtdeOS DESC;
```

---

**Próximos passos**

1. Ajuste as condições de `WHERE` com o período desejado.
2. Substitua placeholders (por exemplo, `:data_inicial`) pela sintaxe de parâmetros do seu BI.
3. Integre essas queries no seu dashboard para visualizar em tempo real o desempenho de atendimento e o perfil dos seus clientes/pacientes.
4. A partir dessas bases, você pode estender com métricas de receita (caso exista tabela de preços) e de satisfação (se tiver registro de feedback).
