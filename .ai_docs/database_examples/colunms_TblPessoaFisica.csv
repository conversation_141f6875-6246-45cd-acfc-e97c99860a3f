"COLUMN_NAME","DATA_TYPE","ORDINAL_POSITION"
ID,bigint,1
CNS,varchar,2
Cpf,varchar,3
DataNascimento,date,4
Email,varchar,5
EnderecoBairro,varchar,6
EnderecoCep,varchar,7
EnderecoCidade,varchar,8
EnderecoComplemento,varchar,9
EnderecoEstado,varchar,10
EnderecoLogradouro,varchar,11
EnderecoNumero,varchar,12
EnderecoPais,varchar,13
EnderecoTipo,varchar,14
EstrangeiroCidade,varchar,15
EstrangeiroCodigoPostal1,varchar,16
EstrangeiroCodigoPostal2,varchar,17
EstrangeiroEstado,varchar,18
EstrangeiroLogradouro1,varchar,19
EstrangeiroLogradouro2,varchar,20
EstrangeiroNumero,varchar,21
EstrangeiroPaisId,varchar,22
Filiacao,varchar,23
Idade,integer,24
IdadeAparente,varchar,25
<PERSON>dent<PERSON>aca<PERSON>,varchar,26
Local<PERSON><PERSON>al<PERSON>,var<PERSON><PERSON>,27
MobileId,varchar,28
MobileTipo,varchar,29
Nacionalidade,integer,30
Nome,varchar,31
Nome1,varchar,32
Nome2,varchar,33
Nome3,varchar,34
Nome4,varchar,35
Nome5,varchar,36
NomeSocial,varchar,37
NomeSocialInformado,bit,38
PaisId,varchar,39
Passaporte,varchar,40
PermiteComunicacao,varchar,41
RedeSocial,varchar,42
Rg,varchar,43
Sexo,varchar,44
TelefoneDdd,varchar,45
TelefoneDdi,varchar,46
TelefoneNumero,varchar,47
TelefoneObservacao,varchar,48
TelefoneTipo,varchar,49
TipoDocEstrangeiro,integer,50
TipoIdade,varchar,51
VersaoCheck,varchar,52
Vip,bit,53
