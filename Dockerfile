# Dockerfile for NPS WhatsApp Service with IRIS ODBC Driver

FROM --platform=linux/amd64 node:20-slim

# Install dependencies for ODBC and build tools
RUN apt-get update && apt-get install -y \
    unixodbc \
    unixodbc-dev \
    python3 \
    make \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install Node.js dependencies
RUN npm ci

# Copy application code
COPY src/ ./src/
COPY drivers/ ./drivers/
COPY .env.example ./

# Copy Linux IRIS ODBC driver to system location
RUN cp drivers/linux/libirisodbc35.so /usr/lib/ && \
    chmod +x /usr/lib/libirisodbc35.so

# Create ODBC configuration
RUN echo '[InterSystems ODBC35]' > /etc/odbcinst.ini && \
    echo 'Description=InterSystems IRIS ODBC Driver' >> /etc/odbcinst.ini && \
    echo 'Driver=/usr/lib/libirisodbc35.so' >> /etc/odbcinst.ini && \
    echo 'Threading=1' >> /etc/odbcinst.ini && \
    echo 'DontDLClose=1' >> /etc/odbcinst.ini

# Set environment variable to indicate Docker environment
ENV DOCKER_ENV=true

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Default command (can be overridden)
CMD ["npm", "run", "api"]
