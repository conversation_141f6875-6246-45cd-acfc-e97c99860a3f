version: '3.8'

services:
  # NPS API Service
  nps-api:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - API_PORT=3000
      - LOG_LEVEL=info
      # Database configuration
      - IRIS_HOST=${IRIS_HOST}
      - IRIS_PORT=${IRIS_PORT:-1972}
      - IRIS_NAMESPACE=${IRIS_NAMESPACE}
      - IRIS_USER=${IRIS_USER}
      - IRIS_PASSWORD=${IRIS_PASSWORD}
      - IRIS_SSL_MODE=${IRIS_SSL_MODE:-0}
      - IRIS_DRIVER_PATH=system
      # WhatsApp configuration
      - WHATSAPP_ACCESS_TOKEN=${WHATSAPP_ACCESS_TOKEN}
      - WHATSAPP_PHONE_NUMBER_ID=${WHATSAPP_PHONE_NUMBER_ID}
      - WHATSAPP_WEBHOOK_VERIFY_TOKEN=${WHATSAPP_WEBHOOK_VERIFY_TOKEN}
      - NPS_FORM_URL=${NPS_FORM_URL}
      # Email configuration
      - RESEND_API_KEY=${RESEND_API_KEY}
      - EMAIL_FROM=${EMAIL_FROM}
      - EMAIL_TO=${EMAIL_TO}
    volumes:
      - ./logs:/app/logs
    command: npm run api
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # NPS Cron Service (alternative)
  nps-cron:
    build: .
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
      # Database configuration
      - IRIS_HOST=${IRIS_HOST}
      - IRIS_PORT=${IRIS_PORT:-1972}
      - IRIS_NAMESPACE=${IRIS_NAMESPACE}
      - IRIS_USER=${IRIS_USER}
      - IRIS_PASSWORD=${IRIS_PASSWORD}
      - IRIS_SSL_MODE=${IRIS_SSL_MODE:-0}
      - IRIS_DRIVER_PATH=system
      # WhatsApp configuration
      - WHATSAPP_ACCESS_TOKEN=${WHATSAPP_ACCESS_TOKEN}
      - WHATSAPP_PHONE_NUMBER_ID=${WHATSAPP_PHONE_NUMBER_ID}
      - WHATSAPP_WEBHOOK_VERIFY_TOKEN=${WHATSAPP_WEBHOOK_VERIFY_TOKEN}
      - NPS_FORM_URL=${NPS_FORM_URL}
      # Email configuration
      - RESEND_API_KEY=${RESEND_API_KEY}
      - EMAIL_FROM=${EMAIL_FROM}
      - EMAIL_TO=${EMAIL_TO}
      # Cron configuration
      - CRON_SCHEDULE=${CRON_SCHEDULE:-0 10 * * *}
      - RUN_ON_STARTUP=${RUN_ON_STARTUP:-false}
    volumes:
      - ./logs:/app/logs
    command: npm run cron
    restart: unless-stopped
    profiles:
      - cron
