const odbc = require('odbc');

async function testServicesQuery() {
  let connection;
  
  try {
    console.log('🔍 Testing services query...');
    
    const connectionString = `DRIVER=./libirisodbc35.so;SERVER=186.227.206.249;PORT=56774;DATABASE=ADOLFOLUTZ;UID=admin.adolfolutz;PWD=***********;`;
    
    connection = await odbc.connect(connectionString);
    console.log('✅ Connected to database');
    
    // Test simple count query
    const query = `
      SELECT COUNT(*) AS TotalOS
      FROM dado.ArqOrdemServico OS
      WHERE OS.Data >= '2025-05-27'
    `;
    
    console.log('📝 Executing query:', query);
    const result = await connection.query(query);
    console.log('✅ Query result:', result);
    
    // Test TipoAtendimento query
    const typeQuery = `
      SELECT TOP 5
        COALESCE(OS.TipoAtendimento, 'Não Informado') AS TipoAtendimento,
        COUNT(*) AS QtdeOS
      FROM dado.ArqOrdemServico OS
      WHERE OS.Data >= '2025-05-27'
      GROUP BY OS.TipoAtendimento
      ORDER BY COUNT(*) DESC
    `;
    
    console.log('📝 Executing type query:', typeQuery);
    const typeResult = await connection.query(typeQuery);
    console.log('✅ Type query result:', typeResult);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    
  } finally {
    if (connection) {
      await connection.close();
      console.log('🔌 Database connection closed');
    }
  }
}

testServicesQuery();
