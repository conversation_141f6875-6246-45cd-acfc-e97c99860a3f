const odbc = require('odbc');

async function testHourFunction() {
  let connection;
  
  try {
    console.log('🔍 Testing HOUR function...');
    
    const connectionString = `DRIVER=./libirisodbc35.so;SERVER=186.227.206.249;PORT=56774;DATABASE=ADOLFOLUTZ;UID=admin.adolfolutz;PWD=***********;`;
    
    connection = await odbc.connect(connectionString);
    console.log('✅ Connected to database');
    
    // Test simple HOUR function
    const query = `
      SELECT TOP 5
        OS.HoraInicial,
        HOUR(OS.HoraInicial) AS Hora
      FROM dado.ArqOrdemServico OS
      WHERE OS.HoraInicial IS NOT NULL
    `;
    
    console.log('📝 Executing query:', query);
    const result = await connection.query(query);
    console.log('✅ Query result:', result);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    
    // Try alternative syntax
    try {
      console.log('🔄 Trying alternative DATEPART syntax...');
      const altQuery = `
        SELECT TOP 5
          OS.HoraInicial,
          DATEPART(hour, OS.HoraInicial) AS Hora
        FROM dado.ArqOrdemServico OS
        WHERE OS.HoraInicial IS NOT NULL
      `;
      
      console.log('📝 Executing alternative query:', altQuery);
      const altResult = await connection.query(altQuery);
      console.log('✅ Alternative query result:', altResult);
      
    } catch (altError) {
      console.error('❌ Alternative query error:', altError.message);
    }
    
  } finally {
    if (connection) {
      await connection.close();
      console.log('🔌 Database connection closed');
    }
  }
}

testHourFunction();
