import { DashboardLayout } from "@/components/dashboard/layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  MessageSquare, 
  Users, 
  Send, 
  Calendar,
  Filter,
  Plus,
  Search,
  CheckCircle,
  Clock,
  AlertCircle,
  Phone
} from "lucide-react";
import { CampaignStats } from "@/components/campaigns/campaign-stats";
import { PatientSelector } from "@/components/campaigns/patient-selector";
import { CampaignList } from "@/components/campaigns/campaign-list";

export default function CampaignsPage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">WhatsApp Campaigns</h1>
            <p className="text-muted-foreground">
              Manage and create targeted WhatsApp campaigns for patient engagement
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Campaign
            </Button>
          </div>
        </div>

        {/* Campaign Stats */}
        <CampaignStats />

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Patient Selection */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Select Target Patients
                </CardTitle>
                <CardDescription>
                  Filter and select patients for your WhatsApp campaign
                </CardDescription>
              </CardHeader>
              <CardContent>
                <PatientSelector />
              </CardContent>
            </Card>
          </div>

          {/* Campaign Creation */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Campaign Details
                </CardTitle>
                <CardDescription>
                  Configure your campaign settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="campaign-name">Campaign Name</Label>
                  <Input 
                    id="campaign-name" 
                    placeholder="e.g., Monthly Health Checkup Reminder"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="message-template">Message Template</Label>
                  <textarea 
                    id="message-template"
                    className="w-full min-h-[100px] p-3 border rounded-md resize-none"
                    placeholder="Hello {name}, it's time for your monthly health checkup. Schedule your appointment at Adolfo Lutz Laboratory..."
                  />
                  <p className="text-xs text-muted-foreground">
                    Use {"{name}"} to personalize messages
                  </p>
                </div>

                <div className="space-y-2">
                  <Label>Schedule</Label>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Clock className="mr-2 h-4 w-4" />
                      Send Now
                    </Button>
                    <Button variant="outline" size="sm">
                      <Calendar className="mr-2 h-4 w-4" />
                      Schedule
                    </Button>
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <div className="flex items-center justify-between text-sm">
                    <span>Selected Patients:</span>
                    <Badge variant="secondary">247 patients</Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm mt-2">
                    <span>Estimated Cost:</span>
                    <span className="font-medium">R$ 24,70</span>
                  </div>
                </div>

                <Button className="w-full">
                  <Send className="mr-2 h-4 w-4" />
                  Create Campaign
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Recent Campaigns */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Campaigns</CardTitle>
            <CardDescription>Your latest WhatsApp campaigns and their performance</CardDescription>
          </CardHeader>
          <CardContent>
            <CampaignList />
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
