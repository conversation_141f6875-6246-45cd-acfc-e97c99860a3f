import { DashboardLayout } from "@/components/dashboard/layout";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { 
  <PERSON>, 
  UserPlus, 
  UserCheck, 
  TrendingUp,
  Calendar,
  MapPin,
  Clock,
  Filter
} from "lucide-react";
import { PatientAgeChart } from "@/components/charts/patient-age-chart";
import { PatientGenderChart } from "@/components/charts/patient-gender-chart";
import { PatientLocationChart } from "@/components/charts/patient-location-chart";
import { PatientActivityChart } from "@/components/charts/patient-activity-chart";

export default function PatientsPage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Patient Analytics</h1>
            <p className="text-muted-foreground">
              Comprehensive analysis of patient demographics and behavior
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>
            <Badge variant="outline" className="text-sm">
              <Calendar className="mr-1 h-3 w-3" />
              Last 30 days
            </Badge>
          </div>
        </div>

        {/* Key Patient Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Patients</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">12,847</div>
              <p className="text-xs text-muted-foreground">
                +5.2% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">New Patients</CardTitle>
              <UserPlus className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">+427</div>
              <p className="text-xs text-muted-foreground">
                +12% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Returning Patients</CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">8,234</div>
              <p className="text-xs text-muted-foreground">
                64% of total patients
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg. Visit Interval</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">45 days</div>
              <p className="text-xs text-muted-foreground">
                -3 days from last month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts Grid */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Age Distribution</CardTitle>
              <CardDescription>Patient distribution by age groups</CardDescription>
            </CardHeader>
            <CardContent>
              <PatientAgeChart />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Gender Distribution</CardTitle>
              <CardDescription>Patient distribution by gender</CardDescription>
            </CardHeader>
            <CardContent>
              <PatientGenderChart />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Geographic Distribution</CardTitle>
              <CardDescription>Top 10 cities by patient count</CardDescription>
            </CardHeader>
            <CardContent>
              <PatientLocationChart />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Patient Activity</CardTitle>
              <CardDescription>New vs returning patients over time</CardDescription>
            </CardHeader>
            <CardContent>
              <PatientActivityChart />
            </CardContent>
          </Card>
        </div>

        {/* Top Patients Table */}
        <Card>
          <CardHeader>
            <CardTitle>Top Patients by Visit Frequency</CardTitle>
            <CardDescription>Patients with the highest number of service orders</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { name: "Maria Silva Santos", cpf: "***.***.***-12", visits: 24, lastVisit: "2 days ago" },
                { name: "João Carlos Oliveira", cpf: "***.***.***-34", visits: 19, lastVisit: "1 week ago" },
                { name: "Ana Paula Costa", cpf: "***.***.***-56", visits: 17, lastVisit: "3 days ago" },
                { name: "Carlos Eduardo Lima", cpf: "***.***.***-78", visits: 15, lastVisit: "5 days ago" },
                { name: "Fernanda Rodrigues", cpf: "***.***.***-90", visits: 14, lastVisit: "1 day ago" },
              ].map((patient, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-blue-600">
                        {patient.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium">{patient.name}</p>
                      <p className="text-sm text-muted-foreground">{patient.cpf}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{patient.visits} visits</p>
                    <p className="text-sm text-muted-foreground">Last: {patient.lastVisit}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
