import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/database';

// Customer service analytics queries
const CUSTOMER_SERVICE_QUERIES = {
  // Support ticket metrics (using OS data as proxy)
  ticketMetrics: `
    SELECT
      COUNT(*) AS TotalTickets,
      COUNT(CASE WHEN OS.OsStatus = 1 THEN 1 END) AS ResolvedTickets,
      COUNT(CASE WHEN OS.OsStatus = 0 THEN 1 END) AS CancelledTickets,
      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS AvgResolutionTimeMin
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
  `,

  // Response time distribution
  responseTimeDistribution: `
    SELECT
      CASE
        WHEN DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal) <= 15 THEN '0-15 min'
        WHEN DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal) <= 30 THEN '16-30 min'
        WHEN DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal) <= 60 THEN '31-60 min'
        ELSE '60+ min'
      END AS TimeRange,
      COUNT(*) AS Count
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
    GROUP BY 1
    ORDER BY 
      CASE TimeRange
        WHEN '0-15 min' THEN 1
        WHEN '16-30 min' THEN 2
        WHEN '31-60 min' THEN 3
        ELSE 4
      END
  `,

  // Daily ticket volume
  dailyTicketVolume: `
    SELECT
      OS.Data,
      COUNT(*) AS TicketCount,
      COUNT(CASE WHEN OS.OsStatus = 1 THEN 1 END) AS ResolvedCount,
      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS AvgTimeMin
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
    GROUP BY OS.Data
    ORDER BY OS.Data
  `,

  // Channel distribution (online vs presential)
  channelDistribution: `
    SELECT
      CASE 
        WHEN OS.DisponivelWeb = 1 THEN 'Online'
        ELSE 'Presencial'
      END AS Channel,
      COUNT(*) AS Count,
      COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() AS Percentage
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
    GROUP BY OS.DisponivelWeb
  `,

  // Agent performance
  agentPerformance: `
    SELECT
      OS.Recepcionista AS AgentId,
      COUNT(*) AS TicketsHandled,
      COUNT(CASE WHEN OS.OsStatus = 1 THEN 1 END) AS TicketsResolved,
      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS AvgResolutionTimeMin,
      COUNT(CASE WHEN OS.OsStatus = 1 THEN 1 END) * 100.0 / COUNT(*) AS ResolutionRate
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND OS.Recepcionista IS NOT NULL
      AND OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
    GROUP BY OS.Recepcionista
    ORDER BY TicketsHandled DESC
    FETCH FIRST 10 ROWS ONLY
  `,

  // Service type distribution
  serviceTypeDistribution: `
    SELECT
      COALESCE(OS.TipoAtendimento, 'Não especificado') AS ServiceType,
      COUNT(*) AS Count,
      COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() AS Percentage
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
    GROUP BY OS.TipoAtendimento
    ORDER BY Count DESC
  `
};

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Execute all customer service analytics queries
    const [
      ticketMetrics,
      responseTimeDistribution,
      dailyTicketVolume,
      channelDistribution,
      agentPerformance,
      serviceTypeDistribution
    ] = await Promise.all([
      executeQuery(CUSTOMER_SERVICE_QUERIES.ticketMetrics),
      executeQuery(CUSTOMER_SERVICE_QUERIES.responseTimeDistribution),
      executeQuery(CUSTOMER_SERVICE_QUERIES.dailyTicketVolume),
      executeQuery(CUSTOMER_SERVICE_QUERIES.channelDistribution),
      executeQuery(CUSTOMER_SERVICE_QUERIES.agentPerformance),
      executeQuery(CUSTOMER_SERVICE_QUERIES.serviceTypeDistribution)
    ]);

    // Calculate derived metrics
    const totalTickets = ticketMetrics[0]?.TotalTickets || 0;
    const resolvedTickets = ticketMetrics[0]?.ResolvedTickets || 0;
    const avgResolutionTime = Math.round(ticketMetrics[0]?.AvgResolutionTimeMin || 0);
    const resolutionRate = totalTickets > 0 ? (resolvedTickets / totalTickets) * 100 : 0;

    // Mock satisfaction data (would come from surveys)
    const satisfactionScore = 4.3;
    const satisfactionTrend = [4.1, 4.2, 4.3, 4.4, 4.3, 4.2, 4.3];

    // Format the response
    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      data: {
        metrics: {
          totalTickets,
          resolvedTickets,
          avgResolutionTime,
          resolutionRate: parseFloat(resolutionRate.toFixed(1)),
          satisfactionScore,
          firstContactResolution: 78.5 // Mock data
        },
        trends: {
          volume: dailyTicketVolume.map(row => ({
            date: row.Data,
            total: parseInt(row.TicketCount),
            resolved: parseInt(row.ResolvedCount),
            avgTime: Math.round(row.AvgTimeMin || 0)
          })),
          satisfaction: satisfactionTrend.map((score, index) => ({
            week: `Week ${index + 1}`,
            score
          }))
        },
        distribution: {
          responseTime: responseTimeDistribution.map(row => ({
            range: row.TimeRange,
            count: parseInt(row.Count)
          })),
          channels: channelDistribution.map(row => ({
            channel: row.Channel,
            count: parseInt(row.Count),
            percentage: parseFloat(row.Percentage.toFixed(1))
          })),
          serviceTypes: serviceTypeDistribution.map(row => ({
            type: row.ServiceType,
            count: parseInt(row.Count),
            percentage: parseFloat(row.Percentage.toFixed(1))
          }))
        },
        agents: agentPerformance.map((row, index) => {
          // Mock agent names for demo
          const agentNames = [
            'Ana Silva', 'Carlos Santos', 'Maria Costa', 'João Oliveira',
            'Paula Lima', 'Roberto Silva', 'Fernanda Cruz', 'Lucas Pereira',
            'Juliana Alves', 'Pedro Rocha'
          ];

          return {
            id: row.AgentId,
            name: agentNames[index] || `Agent ${row.AgentId}`,
            ticketsHandled: parseInt(row.TicketsHandled),
            ticketsResolved: parseInt(row.TicketsResolved),
            avgResolutionTime: Math.round(row.AvgResolutionTimeMin || 0),
            resolutionRate: parseFloat((row.ResolutionRate || 0).toFixed(1)),
            satisfactionScore: 4.0 + Math.random() * 1.0 // Mock satisfaction
          };
        }),
        recentTickets: [
          // Mock recent tickets for demo
          {
            id: 'T001',
            subject: 'Problema com agendamento online',
            status: 'open',
            priority: 'high',
            agent: 'Ana Silva',
            customer: 'João Santos',
            createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            channel: 'email'
          },
          {
            id: 'T002',
            subject: 'Dúvida sobre resultado de exame',
            status: 'resolved',
            priority: 'medium',
            agent: 'Carlos Santos',
            customer: 'Maria Oliveira',
            createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
            channel: 'phone'
          },
          {
            id: 'T003',
            subject: 'Solicitação de segunda via',
            status: 'in_progress',
            priority: 'low',
            agent: 'Maria Costa',
            customer: 'Pedro Silva',
            createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
            channel: 'whatsapp'
          }
        ]
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error fetching customer service analytics:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch customer service analytics',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
