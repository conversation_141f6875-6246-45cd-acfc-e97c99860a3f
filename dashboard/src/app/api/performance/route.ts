import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/database';

// Performance analytics queries
const PERFORMANCE_QUERIES = {
  // Overall efficiency metrics
  overallMetrics: `
    SELECT
      COUNT(*) AS TotalOS,
      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS TempoMedioMin,
      COUNT(CASE WHEN OS.ReimpressaoData IS NULL THEN 1 END) * 100.0 / COUNT(*) AS EficienciaPerc,
      COUNT(DISTINCT OS.Paciente) AS PacientesAtendidos
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
  `,

  // Staff performance
  staffPerformance: `
    SELECT
      OS.Recepcionista AS StaffId,
      COUNT(*) AS QtdeOS,
      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS TempoMedioMin,
      COUNT(CASE WHEN OS.ReimpressaoData IS NULL THEN 1 END) * 100.0 / COUNT(*) AS EficienciaPerc
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND OS.Recepcionista IS NOT NULL
      AND OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
    GROUP BY OS.Recepcionista
    ORDER BY QtdeOS DESC
  `,

  // Performance trend over time (last 6 months)
  performanceTrend: `
    SELECT
      YEAR(OS.Data) AS Ano,
      MONTH(OS.Data) AS Mes,
      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS TempoMedioMin,
      COUNT(CASE WHEN OS.ReimpressaoData IS NULL THEN 1 END) * 100.0 / COUNT(*) AS EficienciaPerc,
      COUNT(DISTINCT OS.Paciente) AS PacientesAtendidos
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('month', -6, CURRENT_DATE)
      AND OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
    GROUP BY YEAR(OS.Data), MONTH(OS.Data)
    ORDER BY Ano, Mes
  `,

  // Daily efficiency trend (last 15 days)
  dailyEfficiency: `
    SELECT
      OS.Data,
      COUNT(*) AS QtdeOS,
      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS TempoMedioMin,
      COUNT(CASE WHEN OS.ReimpressaoData IS NULL THEN 1 END) * 100.0 / COUNT(*) AS EficienciaPerc
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -15, CURRENT_DATE)
      AND OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
    GROUP BY OS.Data
    ORDER BY OS.Data
  `,

  // Service quality metrics
  qualityMetrics: `
    SELECT
      COUNT(CASE WHEN OS.ReimpressaoData IS NULL THEN 1 END) * 100.0 / COUNT(*) AS PrimeiraResolucaoPerc,
      COUNT(CASE WHEN OS.OsStatus = 1 THEN 1 END) * 100.0 / COUNT(*) AS TaxaSucessoPerc,
      COUNT(CASE WHEN OS.OsStatus = 0 THEN 1 END) * 100.0 / COUNT(*) AS TaxaCancelamentoPerc
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
  `
};

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Execute all performance analytics queries
    const [
      overallMetrics,
      staffPerformance,
      performanceTrend,
      dailyEfficiency,
      qualityMetrics
    ] = await Promise.all([
      executeQuery(PERFORMANCE_QUERIES.overallMetrics),
      executeQuery(PERFORMANCE_QUERIES.staffPerformance),
      executeQuery(PERFORMANCE_QUERIES.performanceTrend),
      executeQuery(PERFORMANCE_QUERIES.dailyEfficiency),
      executeQuery(PERFORMANCE_QUERIES.qualityMetrics)
    ]);

    // Calculate overall efficiency score
    const efficiency = overallMetrics[0]?.EficienciaPerc || 0;
    const avgServiceTime = overallMetrics[0]?.TempoMedioMin || 0;
    const qualityScore = qualityMetrics[0]?.PrimeiraResolucaoPerc || 0;
    
    // Mock patient satisfaction (would come from a separate survey system)
    const patientSatisfaction = 4.6;
    const staffProductivity = Math.min(100, Math.max(0, 100 - (avgServiceTime - 10) * 2));

    // Format the response
    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      data: {
        metrics: {
          overallEfficiency: parseFloat(efficiency.toFixed(1)),
          patientSatisfaction: patientSatisfaction,
          staffProductivity: parseFloat(staffProductivity.toFixed(1)),
          averageWaitTime: Math.round(avgServiceTime),
          totalPatients: overallMetrics[0]?.PacientesAtendidos || 0,
          totalOrders: overallMetrics[0]?.TotalOS || 0
        },
        quality: {
          firstTimeResolution: parseFloat((qualityMetrics[0]?.PrimeiraResolucaoPerc || 0).toFixed(1)),
          successRate: parseFloat((qualityMetrics[0]?.TaxaSucessoPerc || 0).toFixed(1)),
          cancellationRate: parseFloat((qualityMetrics[0]?.TaxaCancelamentoPerc || 0).toFixed(1))
        },
        trends: {
          performance: performanceTrend.map(row => ({
            month: `${row.Ano}-${String(row.Mes).padStart(2, '0')}`,
            efficiency: parseFloat((row.EficienciaPerc || 0).toFixed(1)),
            averageTime: Math.round(row.TempoMedioMin || 0),
            patients: parseInt(row.PacientesAtendidos)
          })),
          daily: dailyEfficiency.map(row => ({
            date: row.Data,
            efficiency: parseFloat((row.EficienciaPerc || 0).toFixed(1)),
            orders: parseInt(row.QtdeOS),
            averageTime: Math.round(row.TempoMedioMin || 0)
          }))
        },
        staff: staffPerformance.map((row, index) => {
          // Mock staff names for demo
          const staffNames = [
            'Ana Silva', 'Carlos Santos', 'Maria Costa', 'João Oliveira',
            'Paula Lima', 'Roberto Silva', 'Fernanda Cruz', 'Lucas Pereira'
          ];
          
          const performanceScore = Math.min(100, Math.max(0, 
            (row.EficienciaPerc || 0) * 0.6 + 
            Math.max(0, 100 - (row.TempoMedioMin - 10) * 2) * 0.4
          ));

          return {
            id: row.StaffId,
            name: staffNames[index] || `Staff ${row.StaffId}`,
            orders: parseInt(row.QtdeOS),
            averageTime: Math.round(row.TempoMedioMin || 0),
            efficiency: parseFloat((row.EficienciaPerc || 0).toFixed(1)),
            performanceScore: Math.round(performanceScore)
          };
        }).slice(0, 8), // Top 8 performers
        operational: {
          resourceUtilization: 88,
          equipmentUptime: 95,
          processAutomation: 72
        }
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error fetching performance analytics:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch performance analytics',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
