import { NextRequest, NextResponse } from 'next/server';
import { executeQuery, formatWhatsAppNumber } from '@/lib/database';

// Campaign and patient selection queries
const CAMPAIGN_QUERIES = {
  // Get patients for WhatsApp campaigns with filters
  eligiblePatients: `
    SELECT
      OS.ID,
      OS.CodigoOs,
      OS.Data,
      OS.HoraInicial,
      OS.Paciente,
      PF.Nome,
      PF.Email,
      PF.TelefoneDdd,
      PF.TelefoneNumero,
      PF.EnderecoCidade,
      PF.DataNascimento,
      COUNT(*) OVER (PARTITION BY OS.Paciente) AS TotalVisitas
    FROM dado.ArqOrdemServico OS
    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID
    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND PF.TelefoneNumero IS NOT NULL
      AND PF.TelefoneNumero != ''
      AND PF.Nome IS NOT NULL
  `,

  // Campaign statistics
  campaignStats: `
    SELECT
      COUNT(DISTINCT OS.Paciente) AS TotalPacientesElegiveis,
      COUNT(DISTINCT CASE WHEN OS.Data >= DATEADD('day', -7, CURRENT_DATE) THEN OS.Paciente END) AS PacientesUltimaSemana,
      COUNT(DISTINCT CASE WHEN OS.Data >= DATEADD('day', -30, CURRENT_DATE) THEN OS.Paciente END) AS PacientesUltimoMes
    FROM dado.ArqOrdemServico OS
    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID
    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID
    WHERE PF.TelefoneNumero IS NOT NULL
      AND PF.TelefoneNumero != ''
  `,

  // Cities for filtering
  availableCities: `
    SELECT DISTINCT
      PF.EnderecoCidade AS Cidade,
      COUNT(*) AS QtdePacientes
    FROM dado.ArqOrdemServico OS
    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID
    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID
    WHERE OS.Data >= DATEADD('day', -90, CURRENT_DATE)
      AND PF.EnderecoCidade IS NOT NULL
      AND PF.EnderecoCidade != ''
    GROUP BY PF.EnderecoCidade
    ORDER BY QtdePacientes DESC
  `
};

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'stats';

    if (action === 'patients') {
      return await getEligiblePatients(searchParams);
    } else if (action === 'cities') {
      return await getAvailableCities();
    } else {
      return await getCampaignStats();
    }

  } catch (error) {
    console.error('Error in campaigns API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch campaign data',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

async function getCampaignStats() {
  const [campaignStats] = await Promise.all([
    executeQuery(CAMPAIGN_QUERIES.campaignStats)
  ]);

  const response = {
    success: true,
    timestamp: new Date().toISOString(),
    data: {
      stats: {
        totalEligible: campaignStats[0]?.TotalPacientesElegiveis || 0,
        lastWeek: campaignStats[0]?.PacientesUltimaSemana || 0,
        lastMonth: campaignStats[0]?.PacientesUltimoMes || 0
      },
      // Mock campaign history for now
      recentCampaigns: [
        {
          id: 1,
          name: "Pesquisa de Satisfação - Dezembro",
          status: "completed",
          recipients: 245,
          responses: 89,
          responseRate: 36.3,
          cost: 12.25,
          createdAt: "2024-12-15T10:00:00Z"
        },
        {
          id: 2,
          name: "Lembrete de Retorno",
          status: "active",
          recipients: 156,
          responses: 23,
          responseRate: 14.7,
          cost: 7.80,
          createdAt: "2024-12-20T14:30:00Z"
        },
        {
          id: 3,
          name: "Promoção Exames Preventivos",
          status: "scheduled",
          recipients: 320,
          responses: 0,
          responseRate: 0,
          cost: 16.00,
          createdAt: "2024-12-22T09:00:00Z"
        }
      ]
    }
  };

  return NextResponse.json(response);
}

async function getAvailableCities() {
  const cities = await executeQuery(CAMPAIGN_QUERIES.availableCities);

  const response = {
    success: true,
    timestamp: new Date().toISOString(),
    data: {
      cities: cities.map(row => ({
        name: row.Cidade,
        patientCount: parseInt(row.QtdePacientes)
      }))
    }
  };

  return NextResponse.json(response);
}

async function getEligiblePatients(searchParams: URLSearchParams) {
  // Get filter parameters
  const city = searchParams.get('city');
  const minAge = searchParams.get('minAge');
  const maxAge = searchParams.get('maxAge');
  const lastVisitDays = searchParams.get('lastVisitDays') || '30';
  const search = searchParams.get('search');
  const limit = parseInt(searchParams.get('limit') || '100');

  // Build dynamic query with filters
  let query = CAMPAIGN_QUERIES.eligiblePatients;
  const conditions = [];

  if (city && city !== 'all') {
    conditions.push(`PF.EnderecoCidade = '${city}'`);
  }

  if (minAge) {
    conditions.push(`DATEDIFF('year', PF.DataNascimento, CURRENT_DATE) >= ${minAge}`);
  }

  if (maxAge) {
    conditions.push(`DATEDIFF('year', PF.DataNascimento, CURRENT_DATE) <= ${maxAge}`);
  }

  if (lastVisitDays) {
    conditions.push(`OS.Data >= DATEADD('day', -${lastVisitDays}, CURRENT_DATE)`);
  }

  if (search) {
    conditions.push(`(PF.Nome LIKE '%${search}%' OR PF.TelefoneNumero LIKE '%${search}%')`);
  }

  if (conditions.length > 0) {
    query += ' AND ' + conditions.join(' AND ');
  }

  query += ` ORDER BY OS.Data DESC FETCH FIRST ${limit} ROWS ONLY`;

  const patients = await executeQuery(query);

  // Process patients and format WhatsApp numbers
  const processedPatients = patients.map(row => {
    const telefoneCompleto = row.TelefoneDdd && row.TelefoneNumero
      ? `${row.TelefoneDdd}${row.TelefoneNumero}`
      : row.TelefoneNumero;

    const whatsappNumber = formatWhatsAppNumber(telefoneCompleto);
    const age = row.DataNascimento 
      ? new Date().getFullYear() - new Date(row.DataNascimento).getFullYear()
      : null;

    return {
      id: row.ID,
      codigoOs: row.CodigoOs,
      data: row.Data,
      pacienteId: row.Paciente,
      nome: row.Nome,
      email: row.Email?.trim().toLowerCase(),
      telefone: telefoneCompleto?.trim(),
      whatsapp: whatsappNumber,
      cidade: row.EnderecoCidade,
      idade: age,
      totalVisitas: parseInt(row.TotalVisitas)
    };
  }).filter(patient => patient.whatsapp); // Only include patients with valid WhatsApp

  const response = {
    success: true,
    timestamp: new Date().toISOString(),
    data: {
      patients: processedPatients,
      total: processedPatients.length,
      filters: {
        city,
        minAge,
        maxAge,
        lastVisitDays,
        search,
        limit
      }
    }
  };

  return NextResponse.json(response);
}
