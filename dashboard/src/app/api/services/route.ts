import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/database';

// Service analytics queries
const SERVICE_QUERIES = {
  // Total service orders
  totalOrders: `
    SELECT COUNT(*) AS TotalOS
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
  `,

  // Average service time
  averageServiceTime: `
    SELECT
      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS TempoMedioAtendimentoMin
    FROM dado.ArqOrdemServico OS
    WHERE OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
      AND OS.Data >= DATEADD('day', -30, CURRENT_DATE)
  `,

  // Service volume trend (last 30 days)
  volumeTrend: `
    SELECT
      OS.Data,
      COUNT(*) AS QtdeOS
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
    GROUP BY OS.Data
    ORDER BY OS.Data
  `,

  // Service distribution by type
  serviceTypes: `
    SELECT
      OS.TipoAtendimento,
      COUNT(*) AS QtdeOS,
      COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() AS PercOS
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND OS.TipoAtendimento IS NOT NULL
    GROUP BY OS.TipoAtendimento
    ORDER BY QtdeOS DESC
  `,

  // Service time distribution by hour
  timeDistribution: `
    SELECT
      DATEPART('hour', OS.HoraInicial) AS Hora,
      COUNT(*) AS QtdeOS
    FROM dado.ArqOrdemServico OS
    WHERE OS.HoraInicial IS NOT NULL
      AND OS.Data >= DATEADD('day', -30, CURRENT_DATE)
    GROUP BY DATEPART('hour', OS.HoraInicial)
    ORDER BY Hora
  `,

  // Weekly patterns
  weeklyPattern: `
    SELECT
      DATEPART('weekday', OS.Data) AS DiaSemana,
      COUNT(*) AS QtdeOS
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
    GROUP BY DATEPART('weekday', OS.Data)
    ORDER BY DiaSemana
  `,

  // Online vs Presential
  onlineVsPresential: `
    SELECT
      CASE 
        WHEN OS.DisponivelWeb = 1 THEN 'Online'
        ELSE 'Presencial'
      END AS TipoAgendamento,
      COUNT(*) AS QtdeOS,
      COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() AS PercOS
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
    GROUP BY OS.DisponivelWeb
  `,

  // Staff performance
  staffPerformance: `
    SELECT
      OS.Recepcionista AS ID_Recepcionista,
      COUNT(*) AS QtdeOS,
      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS TempoMedioMin
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND OS.Recepcionista IS NOT NULL
      AND OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
    GROUP BY OS.Recepcionista
    ORDER BY QtdeOS DESC
    FETCH FIRST 10 ROWS ONLY
  `,

  // Reprint rate
  reprintRate: `
    SELECT
      COUNT(*) * 100.0 / (
        SELECT COUNT(*) 
        FROM dado.ArqOrdemServico 
        WHERE Data >= DATEADD('day', -30, CURRENT_DATE)
      ) AS TaxaReimpressaoPerc
    FROM dado.ArqOrdemServico OS
    WHERE OS.ReimpressaoData IS NOT NULL
      AND OS.Data >= DATEADD('day', -30, CURRENT_DATE)
  `
};

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Execute all service analytics queries
    const [
      totalOrders,
      averageServiceTime,
      volumeTrend,
      serviceTypes,
      timeDistribution,
      weeklyPattern,
      onlineVsPresential,
      staffPerformance,
      reprintRate
    ] = await Promise.all([
      executeQuery(SERVICE_QUERIES.totalOrders),
      executeQuery(SERVICE_QUERIES.averageServiceTime),
      executeQuery(SERVICE_QUERIES.volumeTrend),
      executeQuery(SERVICE_QUERIES.serviceTypes),
      executeQuery(SERVICE_QUERIES.timeDistribution),
      executeQuery(SERVICE_QUERIES.weeklyPattern),
      executeQuery(SERVICE_QUERIES.onlineVsPresential),
      executeQuery(SERVICE_QUERIES.staffPerformance),
      executeQuery(SERVICE_QUERIES.reprintRate)
    ]);

    // Format the response
    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      data: {
        metrics: {
          totalOrders: totalOrders[0]?.TotalOS || 0,
          averageServiceTime: Math.round(averageServiceTime[0]?.TempoMedioAtendimentoMin || 0),
          reprintRate: parseFloat((reprintRate[0]?.TaxaReimpressaoPerc || 0).toFixed(2))
        },
        trends: {
          volume: volumeTrend.map(row => ({
            date: row.Data,
            orders: parseInt(row.QtdeOS)
          })),
          timeDistribution: timeDistribution.map(row => ({
            hour: parseInt(row.Hora),
            orders: parseInt(row.QtdeOS)
          })),
          weeklyPattern: weeklyPattern.map(row => ({
            dayOfWeek: parseInt(row.DiaSemana),
            orders: parseInt(row.QtdeOS)
          }))
        },
        distribution: {
          serviceTypes: serviceTypes.map(row => ({
            type: row.TipoAtendimento,
            count: parseInt(row.QtdeOS),
            percentage: parseFloat(row.PercOS.toFixed(2))
          })),
          onlineVsPresential: onlineVsPresential.map(row => ({
            type: row.TipoAgendamento,
            count: parseInt(row.QtdeOS),
            percentage: parseFloat(row.PercOS.toFixed(2))
          }))
        },
        staff: staffPerformance.map(row => ({
          id: row.ID_Recepcionista,
          orders: parseInt(row.QtdeOS),
          averageTime: Math.round(row.TempoMedioMin || 0)
        }))
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error fetching service analytics:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch service analytics',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
