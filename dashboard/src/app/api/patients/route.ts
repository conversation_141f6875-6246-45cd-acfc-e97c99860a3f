import { NextRequest, NextResponse } from 'next/server';
import { executeQuery, formatWhatsAppNumber } from '@/lib/database';

// Patient analytics queries
const PATIENT_QUERIES = {
  // Active patients count
  activePatients: `
    SELECT COUNT(DISTINCT OS.Paciente) AS PacientesAtivos
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
  `,

  // New patients in period
  newPatients: `
    SELECT COUNT(*) AS NovosPacientes
    FROM dado.ArqPaciente P
    WHERE P.PrimeiraOS >= DATEADD('day', -30, CURRENT_DATE)
  `,

  // Patient age distribution
  ageDistribution: `
    SELECT
      CASE
        WHEN DATEDIFF('year', PF.DataNascimento, CURRENT_DATE) < 18 THEN '<18'
        WHEN DATEDIFF('year', PF.DataNascimento, CURRENT_DATE) BETWEEN 18 AND 30 THEN '18-30'
        WHEN DATEDIFF('year', PF.DataNascimento, CURRENT_DATE) BETWEEN 31 AND 50 THEN '31-50'
        WHEN DATEDIFF('year', PF.DataNascimento, CURRENT_DATE) BETWEEN 51 AND 65 THEN '51-65'
        ELSE '65+' 
      END AS FaixaIdade,
      COUNT(DISTINCT AP.ID) AS QtdePacientes
    FROM dado.TblPessoaFisica PF
    JOIN dado.ArqPaciente AP ON AP.PessoaFisica = PF.ID
    JOIN dado.ArqOrdemServico OS ON OS.Paciente = AP.ID
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
    GROUP BY 1
    ORDER BY FaixaIdade
  `,

  // Gender distribution
  genderDistribution: `
    SELECT
      PF.Sexo,
      COUNT(DISTINCT AP.ID) AS QtdePacientes
    FROM dado.TblPessoaFisica PF
    JOIN dado.ArqPaciente AP ON AP.PessoaFisica = PF.ID
    JOIN dado.ArqOrdemServico OS ON OS.Paciente = AP.ID
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
    GROUP BY PF.Sexo
  `,

  // Geographic distribution
  locationDistribution: `
    SELECT
      PF.EnderecoCidade AS Cidade,
      COUNT(DISTINCT AP.ID) AS QtdePacientes
    FROM dado.TblPessoaFisica PF
    JOIN dado.ArqPaciente AP ON AP.PessoaFisica = PF.ID
    JOIN dado.ArqOrdemServico OS ON OS.Paciente = AP.ID
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND PF.EnderecoCidade IS NOT NULL
    GROUP BY PF.EnderecoCidade
    ORDER BY QtdePacientes DESC
    FETCH FIRST 10 ROWS ONLY
  `,

  // Top patients by visit frequency
  topPatients: `
    SELECT
      AP.ID AS PacienteID,
      PF.Nome,
      PF.Cpf,
      COUNT(*) AS QtdeOS,
      MAX(OS.Data) AS UltimaVisita
    FROM dado.ArqOrdemServico OS
    JOIN dado.ArqPaciente AP ON OS.Paciente = AP.ID
    JOIN dado.TblPessoaFisica PF ON AP.PessoaFisica = PF.ID
    WHERE OS.Data >= DATEADD('day', -90, CURRENT_DATE)
    GROUP BY AP.ID, PF.Nome, PF.Cpf
    ORDER BY QtdeOS DESC
    FETCH FIRST 10 ROWS ONLY
  `,

  // Patient activity trend (last 30 days)
  activityTrend: `
    SELECT
      OS.Data,
      COUNT(DISTINCT OS.Paciente) AS PacientesAtivos,
      COUNT(*) AS TotalOS
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
    GROUP BY OS.Data
    ORDER BY OS.Data
  `
};

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Execute all patient analytics queries
    const [
      activePatients,
      newPatients,
      ageDistribution,
      genderDistribution,
      locationDistribution,
      topPatients,
      activityTrend
    ] = await Promise.all([
      executeQuery(PATIENT_QUERIES.activePatients),
      executeQuery(PATIENT_QUERIES.newPatients),
      executeQuery(PATIENT_QUERIES.ageDistribution),
      executeQuery(PATIENT_QUERIES.genderDistribution),
      executeQuery(PATIENT_QUERIES.locationDistribution),
      executeQuery(PATIENT_QUERIES.topPatients),
      executeQuery(PATIENT_QUERIES.activityTrend)
    ]);

    // Format the response
    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      data: {
        metrics: {
          activePatients: activePatients[0]?.PacientesAtivos || 0,
          newPatients: newPatients[0]?.NovosPacientes || 0,
        },
        demographics: {
          age: ageDistribution.map(row => ({
            ageGroup: row.FaixaIdade,
            count: parseInt(row.QtdePacientes)
          })),
          gender: genderDistribution.map(row => ({
            gender: row.Sexo,
            count: parseInt(row.QtdePacientes)
          })),
          location: locationDistribution.map(row => ({
            city: row.Cidade,
            count: parseInt(row.QtdePacientes)
          }))
        },
        topPatients: topPatients.map(row => ({
          id: row.PacienteID,
          name: row.Nome,
          cpf: row.Cpf,
          visitCount: parseInt(row.QtdeOS),
          lastVisit: row.UltimaVisita
        })),
        activityTrend: activityTrend.map(row => ({
          date: row.Data,
          activePatients: parseInt(row.PacientesAtivos),
          totalOrders: parseInt(row.TotalOS)
        }))
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error fetching patient analytics:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch patient analytics',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
