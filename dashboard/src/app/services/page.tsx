import { DashboardLayout } from "@/components/dashboard/layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { 
  BarChart3, 
  Clock, 
  RefreshCw, 
  TrendingUp,
  Calendar,
  Filter,
  Users,
  CheckCircle,
  AlertTriangle
} from "lucide-react";
import { ServiceVolumeChart } from "@/components/charts/service-volume-chart";
import { ServiceTimeChart } from "@/components/charts/service-time-chart";
import { ServiceTypeChart } from "@/components/charts/service-type-chart";
import { ServiceWeekdayChart } from "@/components/charts/service-weekday-chart";

export default function ServicesPage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Service Analytics</h1>
            <p className="text-muted-foreground">
              Analysis of service orders, performance metrics, and operational efficiency
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>
            <Badge variant="outline" className="text-sm">
              <Calendar className="mr-1 h-3 w-3" />
              Last 30 days
            </Badge>
          </div>
        </div>

        {/* Key Service Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Service Orders</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">8,547</div>
              <p className="text-xs text-muted-foreground">
                +18% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg. Service Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">8.5 min</div>
              <p className="text-xs text-muted-foreground">
                -2 min from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Reprint Rate</CardTitle>
              <RefreshCw className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3.2%</div>
              <p className="text-xs text-muted-foreground">
                -0.5% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Online Scheduling</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">67%</div>
              <p className="text-xs text-muted-foreground">
                +5% from last month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts Grid */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Service Volume Trend</CardTitle>
              <CardDescription>Daily service orders over the last 30 days</CardDescription>
            </CardHeader>
            <CardContent>
              <ServiceVolumeChart />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Service Time Distribution</CardTitle>
              <CardDescription>Average service time by hour of day</CardDescription>
            </CardHeader>
            <CardContent>
              <ServiceTimeChart />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Service Type Distribution</CardTitle>
              <CardDescription>Breakdown by type of service</CardDescription>
            </CardHeader>
            <CardContent>
              <ServiceTypeChart />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Weekly Pattern</CardTitle>
              <CardDescription>Service orders by day of the week</CardDescription>
            </CardHeader>
            <CardContent>
              <ServiceWeekdayChart />
            </CardContent>
          </Card>
        </div>

        {/* Performance Indicators */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Service Quality</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm">Completed Orders</span>
                  </div>
                  <span className="font-medium">96.8%</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    <span className="text-sm">Cancelled Orders</span>
                  </div>
                  <span className="font-medium">2.1%</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <RefreshCw className="h-4 w-4 text-blue-600" />
                    <span className="text-sm">Rescheduled</span>
                  </div>
                  <span className="font-medium">1.1%</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Staff Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Top Performer</span>
                  <span className="font-medium">Ana Silva</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Avg. Orders/Day</span>
                  <span className="font-medium">45</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Active Staff</span>
                  <span className="font-medium">12</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Peak Hours</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Morning Peak</span>
                  <span className="font-medium">8:00 - 10:00</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Afternoon Peak</span>
                  <span className="font-medium">14:00 - 16:00</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Busiest Day</span>
                  <span className="font-medium">Monday</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
