import { DashboardLayout } from "@/components/dashboard/layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Headphones, 
  MessageCircle, 
  Phone,
  Mail,
  Clock,
  CheckCircle,
  AlertTriangle,
  Users,
  Calendar,
  Filter,
  Star
} from "lucide-react";
import { CustomerSatisfactionChart } from "@/components/charts/customer-satisfaction-chart";
import { ResponseTimeChart } from "@/components/charts/response-time-chart";
import { TicketVolumeChart } from "@/components/charts/ticket-volume-chart";

export default function CustomerServicePage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Customer Service</h1>
            <p className="text-muted-foreground">
              Monitor customer support metrics and service quality
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>
            <Badge variant="outline" className="text-sm">
              <Calendar className="mr-1 h-3 w-3" />
              Last 30 days
            </Badge>
          </div>
        </div>

        {/* Key Customer Service Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Tickets</CardTitle>
              <Headphones className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,247</div>
              <p className="text-xs text-muted-foreground">
                +8% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg. Response Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2.3h</div>
              <p className="text-xs text-muted-foreground">
                -0.5h from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Resolution Rate</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">94.2%</div>
              <p className="text-xs text-muted-foreground">
                +1.2% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Customer Satisfaction</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">4.6/5</div>
              <p className="text-xs text-muted-foreground">
                +0.1 from last month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts Grid */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Customer Satisfaction Trend</CardTitle>
              <CardDescription>Monthly satisfaction scores and feedback</CardDescription>
            </CardHeader>
            <CardContent>
              <CustomerSatisfactionChart />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Response Time Analysis</CardTitle>
              <CardDescription>Average response times by channel</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponseTimeChart />
            </CardContent>
          </Card>

          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Ticket Volume</CardTitle>
              <CardDescription>Daily ticket volume and resolution trends</CardDescription>
            </CardHeader>
            <CardContent>
              <TicketVolumeChart />
            </CardContent>
          </Card>
        </div>

        {/* Support Channels & Team Performance */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Support Channels</CardTitle>
              <CardDescription>Ticket distribution by communication channel</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-blue-600" />
                    <span className="text-sm">Phone Support</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-20 h-2 bg-muted rounded-full">
                      <div className="w-[45%] h-2 bg-blue-500 rounded-full"></div>
                    </div>
                    <span className="text-sm font-medium">45%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <MessageCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm">WhatsApp</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-20 h-2 bg-muted rounded-full">
                      <div className="w-[35%] h-2 bg-green-500 rounded-full"></div>
                    </div>
                    <span className="text-sm font-medium">35%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-purple-600" />
                    <span className="text-sm">Email</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-20 h-2 bg-muted rounded-full">
                      <div className="w-[20%] h-2 bg-purple-500 rounded-full"></div>
                    </div>
                    <span className="text-sm font-medium">20%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Team Performance</CardTitle>
              <CardDescription>Individual agent performance metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { name: "Ana Silva", tickets: 89, satisfaction: 4.8, responseTime: "1.2h" },
                  { name: "Carlos Santos", tickets: 76, satisfaction: 4.6, responseTime: "1.8h" },
                  { name: "Maria Costa", tickets: 82, satisfaction: 4.7, responseTime: "1.5h" },
                  { name: "João Oliveira", tickets: 71, satisfaction: 4.5, responseTime: "2.1h" },
                ].map((agent, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">{agent.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {agent.tickets} tickets • {agent.satisfaction}/5 rating
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{agent.responseTime}</p>
                      <p className="text-xs text-muted-foreground">avg response</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Tickets */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Support Tickets</CardTitle>
            <CardDescription>Latest customer support requests and their status</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { id: "#1247", customer: "Maria Silva", issue: "Appointment rescheduling", status: "resolved", priority: "medium", time: "2 hours ago" },
                { id: "#1246", customer: "João Santos", issue: "Test results inquiry", status: "in-progress", priority: "high", time: "4 hours ago" },
                { id: "#1245", customer: "Ana Costa", issue: "Payment processing", status: "pending", priority: "low", time: "6 hours ago" },
                { id: "#1244", customer: "Carlos Lima", issue: "Lab location directions", status: "resolved", priority: "low", time: "8 hours ago" },
              ].map((ticket, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div>
                      <p className="font-medium">{ticket.id}</p>
                      <p className="text-sm text-muted-foreground">{ticket.customer}</p>
                    </div>
                    <div>
                      <p className="text-sm">{ticket.issue}</p>
                      <p className="text-xs text-muted-foreground">{ticket.time}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge 
                      variant={ticket.priority === 'high' ? 'destructive' : ticket.priority === 'medium' ? 'default' : 'secondary'}
                    >
                      {ticket.priority}
                    </Badge>
                    <Badge 
                      variant={ticket.status === 'resolved' ? 'default' : ticket.status === 'in-progress' ? 'secondary' : 'outline'}
                      className={ticket.status === 'resolved' ? 'bg-green-100 text-green-800' : ''}
                    >
                      {ticket.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
