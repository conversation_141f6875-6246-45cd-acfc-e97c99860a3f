"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Search, 
  Filter,
  Users,
  MapPin,
  Calendar,
  Phone
} from "lucide-react";

const mockPatients = [
  { id: 1, name: "<PERSON>", phone: "+55 11 99999-1234", city: "São Paulo", lastVisit: "2023-11-15", age: 45, selected: false },
  { id: 2, name: "<PERSON>", phone: "+55 11 99999-5678", city: "São Paulo", lastVisit: "2023-10-20", age: 52, selected: false },
  { id: 3, name: "<PERSON>", phone: "+55 21 99999-9012", city: "Rio de Janeiro", lastVisit: "2023-11-10", age: 38, selected: false },
  { id: 4, name: "<PERSON>", phone: "+55 11 99999-3456", city: "São Paulo", lastVisit: "2023-09-25", age: 61, selected: false },
  { id: 5, name: "Fernanda Rodrigues", phone: "+55 31 99999-7890", city: "Belo Horizonte", lastVisit: "2023-11-18", age: 29, selected: false },
];

export function PatientSelector() {
  const [patients, setPatients] = useState(mockPatients);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCity, setSelectedCity] = useState("");
  const [ageRange, setAgeRange] = useState("");
  const [lastVisitRange, setLastVisitRange] = useState("");

  const selectedCount = patients.filter(p => p.selected).length;

  const handleSelectPatient = (patientId: number, selected: boolean) => {
    setPatients(prev => 
      prev.map(p => 
        p.id === patientId ? { ...p, selected } : p
      )
    );
  };

  const handleSelectAll = () => {
    const allSelected = patients.every(p => p.selected);
    setPatients(prev => 
      prev.map(p => ({ ...p, selected: !allSelected }))
    );
  };

  const filteredPatients = patients.filter(patient => {
    const matchesSearch = patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         patient.phone.includes(searchTerm);
    const matchesCity = !selectedCity || patient.city === selectedCity;
    // Add more filters as needed
    return matchesSearch && matchesCity;
  });

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="space-y-2">
          <Label htmlFor="search">Search Patients</Label>
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              id="search"
              placeholder="Name or phone..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label>City</Label>
          <Select value={selectedCity} onValueChange={setSelectedCity}>
            <SelectTrigger>
              <SelectValue placeholder="All cities" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All cities</SelectItem>
              <SelectItem value="São Paulo">São Paulo</SelectItem>
              <SelectItem value="Rio de Janeiro">Rio de Janeiro</SelectItem>
              <SelectItem value="Belo Horizonte">Belo Horizonte</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Age Range</Label>
          <Select value={ageRange} onValueChange={setAgeRange}>
            <SelectTrigger>
              <SelectValue placeholder="All ages" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All ages</SelectItem>
              <SelectItem value="18-30">18-30 years</SelectItem>
              <SelectItem value="31-50">31-50 years</SelectItem>
              <SelectItem value="51-65">51-65 years</SelectItem>
              <SelectItem value="65+">65+ years</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Last Visit</Label>
          <Select value={lastVisitRange} onValueChange={setLastVisitRange}>
            <SelectTrigger>
              <SelectValue placeholder="Any time" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Any time</SelectItem>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="180">Last 6 months</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Selection Summary */}
      <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
        <div className="flex items-center gap-2">
          <Checkbox
            checked={patients.length > 0 && patients.every(p => p.selected)}
            onCheckedChange={handleSelectAll}
          />
          <span className="text-sm font-medium">
            Select All ({filteredPatients.length} patients)
          </span>
        </div>
        <Badge variant={selectedCount > 0 ? "default" : "secondary"}>
          {selectedCount} selected
        </Badge>
      </div>

      {/* Patient List */}
      <div className="space-y-2 max-h-[400px] overflow-y-auto">
        {filteredPatients.map((patient) => (
          <div
            key={patient.id}
            className={`flex items-center justify-between p-3 border rounded-lg transition-colors ${
              patient.selected ? 'bg-blue-50 border-blue-200' : 'hover:bg-muted/50'
            }`}
          >
            <div className="flex items-center gap-3">
              <Checkbox
                checked={patient.selected}
                onCheckedChange={(checked) => handleSelectPatient(patient.id, checked as boolean)}
              />
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-blue-600">
                  {patient.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                </span>
              </div>
              <div>
                <p className="font-medium">{patient.name}</p>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Phone className="h-3 w-3" />
                    {patient.phone}
                  </span>
                  <span className="flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    {patient.city}
                  </span>
                  <span className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {patient.lastVisit}
                  </span>
                </div>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium">{patient.age} years</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
