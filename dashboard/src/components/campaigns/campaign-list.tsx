import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Users,
  MessageSquare,
  Calendar,
  MoreHorizontal
} from "lucide-react";

const campaigns = [
  {
    id: 1,
    name: "Monthly Health Checkup Reminder",
    status: "completed",
    sentAt: "2023-12-15 09:00",
    recipients: 1247,
    responses: 847,
    responseRate: 68,
    cost: "R$ 124,70"
  },
  {
    id: 2,
    name: "Flu Vaccination Campaign",
    status: "active",
    sentAt: "2023-12-18 14:30",
    recipients: 892,
    responses: 234,
    responseRate: 26,
    cost: "R$ 89,20"
  },
  {
    id: 3,
    name: "Diabetes Prevention Workshop",
    status: "scheduled",
    sentAt: "2023-12-20 10:00",
    recipients: 456,
    responses: 0,
    responseRate: 0,
    cost: "R$ 45,60"
  },
  {
    id: 4,
    name: "Annual Physical Exam Reminder",
    status: "failed",
    sentAt: "2023-12-10 08:00",
    recipients: 234,
    responses: 12,
    responseRate: 5,
    cost: "R$ 23,40"
  },
];

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'completed':
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    case 'active':
      return <Clock className="h-4 w-4 text-blue-600" />;
    case 'scheduled':
      return <Calendar className="h-4 w-4 text-yellow-600" />;
    case 'failed':
      return <AlertCircle className="h-4 w-4 text-red-600" />;
    default:
      return <Clock className="h-4 w-4 text-gray-600" />;
  }
};

const getStatusBadge = (status: string) => {
  switch (status) {
    case 'completed':
      return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>;
    case 'active':
      return <Badge variant="default" className="bg-blue-100 text-blue-800">Active</Badge>;
    case 'scheduled':
      return <Badge variant="default" className="bg-yellow-100 text-yellow-800">Scheduled</Badge>;
    case 'failed':
      return <Badge variant="destructive">Failed</Badge>;
    default:
      return <Badge variant="secondary">Unknown</Badge>;
  }
};

export function CampaignList() {
  return (
    <div className="space-y-4">
      {campaigns.map((campaign) => (
        <div
          key={campaign.id}
          className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
        >
          <div className="flex items-center gap-4">
            {getStatusIcon(campaign.status)}
            <div>
              <h4 className="font-medium">{campaign.name}</h4>
              <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                <span className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {campaign.sentAt}
                </span>
                <span className="flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  {campaign.recipients} recipients
                </span>
                <span className="flex items-center gap-1">
                  <MessageSquare className="h-3 w-3" />
                  {campaign.responses} responses ({campaign.responseRate}%)
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="text-sm font-medium">{campaign.cost}</div>
              {getStatusBadge(campaign.status)}
            </div>
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
}
