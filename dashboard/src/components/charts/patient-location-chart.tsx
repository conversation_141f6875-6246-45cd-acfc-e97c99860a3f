"use client";

import { <PERSON><PERSON><PERSON>, Bar, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const data = [
  { city: 'São Paulo', patients: 3420 },
  { city: 'Rio de Janeiro', patients: 2180 },
  { city: 'Belo Horizonte', patients: 1890 },
  { city: 'Salvador', patients: 1650 },
  { city: 'Brasília', patients: 1420 },
  { city: 'Fortaleza', patients: 1280 },
  { city: 'Curitiba', patients: 1150 },
  { city: 'Recife', patients: 980 },
  { city: 'Porto Alegre', patients: 890 },
  { city: 'Goiânia', patients: 750 },
];

export function PatientLocationChart() {
  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="city" 
            angle={-45}
            textAnchor="end"
            height={80}
            fontSize={12}
          />
          <YAxis />
          <Tooltip />
          <Bar dataKey="patients" fill="#3B82F6" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
