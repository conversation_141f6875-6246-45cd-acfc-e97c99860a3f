"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface LocationData {
  city: string;
  count: number;
}

interface PatientLocationChartProps {
  data?: LocationData[];
}

export function PatientLocationChart({ data }: PatientLocationChartProps) {
  // Transform data for the chart
  const chartData = data?.map(item => ({
    city: item.city,
    patients: item.count
  })) || [];
  if (!chartData.length) {
    return (
      <div className="h-[300px] flex items-center justify-center text-muted-foreground">
        No location distribution data available
      </div>
    );
  }

  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            dataKey="city"
            angle={-45}
            textAnchor="end"
            height={80}
            fontSize={12}
          />
          <YAxis />
          <Tooltip />
          <Bar dataKey="patients" fill="#3B82F6" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
