"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';

const data = [
  { name: '<18', value: 8, color: '#3B82F6' },
  { name: '18-30', value: 25, color: '#10B981' },
  { name: '31-50', value: 35, color: '#F59E0B' },
  { name: '51-65', value: 22, color: '#EF4444' },
  { name: '65+', value: 10, color: '#8B5CF6' },
];

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

export function PatientAgeChart() {
  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}
