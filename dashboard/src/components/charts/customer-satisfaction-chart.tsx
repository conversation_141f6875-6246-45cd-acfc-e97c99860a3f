"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const data = [
  { month: 'Jul', satisfaction: 4.2, responses: 234 },
  { month: 'Aug', satisfaction: 4.3, responses: 267 },
  { month: 'Sep', satisfaction: 4.4, responses: 289 },
  { month: 'Oct', satisfaction: 4.4, responses: 312 },
  { month: 'Nov', satisfaction: 4.5, responses: 298 },
  { month: 'Dec', satisfaction: 4.6, responses: 276 },
];

export function CustomerSatisfactionChart() {
  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" />
          <YAxis 
            domain={[3.8, 5.0]}
            label={{ value: 'Rating', angle: -90, position: 'insideLeft' }}
          />
          <Tooltip 
            formatter={(value, name) => [
              name === 'satisfaction' ? `${value}/5` : value,
              name === 'satisfaction' ? 'Satisfaction' : 'Responses'
            ]}
          />
          <Line 
            type="monotone" 
            dataKey="satisfaction" 
            stroke="#10B981" 
            strokeWidth={3}
            dot={{ fill: '#10B981', strokeWidth: 2, r: 6 }}
            activeDot={{ r: 8 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
