"use client";

import { AreaChart, Area, <PERSON>A<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const data = [
  { date: '01/12', efficiency: 85, waitTime: 15.2, throughput: 245 },
  { date: '02/12', efficiency: 87, waitTime: 14.8, throughput: 267 },
  { date: '03/12', efficiency: 86, waitTime: 15.1, throughput: 289 },
  { date: '04/12', efficiency: 89, waitTime: 13.5, throughput: 312 },
  { date: '05/12', efficiency: 88, waitTime: 14.2, throughput: 298 },
  { date: '06/12', efficiency: 85, waitTime: 15.8, throughput: 276 },
  { date: '07/12', efficiency: 83, waitTime: 16.5, throughput: 234 },
  { date: '08/12', efficiency: 87, waitTime: 14.1, throughput: 287 },
  { date: '09/12', efficiency: 90, waitTime: 12.8, throughput: 301 },
  { date: '10/12', efficiency: 92, waitTime: 11.9, throughput: 324 },
  { date: '11/12', efficiency: 91, waitTime: 12.3, throughput: 345 },
  { date: '12/12', efficiency: 89, waitTime: 13.1, throughput: 367 },
  { date: '13/12', efficiency: 88, waitTime: 13.8, throughput: 389 },
  { date: '14/12', efficiency: 86, waitTime: 14.5, throughput: 356 },
  { date: '15/12', efficiency: 87, waitTime: 14.2, throughput: 334 },
];

export function EfficiencyTrendChart() {
  return (
    <div className="h-[400px]">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={data}
          margin={{
            top: 10,
            right: 30,
            left: 0,
            bottom: 0,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="date" 
            fontSize={12}
          />
          <YAxis />
          <Tooltip />
          <Area 
            type="monotone" 
            dataKey="efficiency" 
            stackId="1"
            stroke="#3B82F6" 
            fill="#3B82F6"
            fillOpacity={0.6}
            name="Efficiency (%)"
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
}
