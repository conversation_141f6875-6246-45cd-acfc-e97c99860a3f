"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

const data = [
  { month: 'Jul', efficiency: 82, satisfaction: 4.2, productivity: 89 },
  { month: 'Aug', efficiency: 84, satisfaction: 4.3, productivity: 91 },
  { month: 'Sep', efficiency: 86, satisfaction: 4.4, productivity: 93 },
  { month: 'Oct', efficiency: 85, satisfaction: 4.4, productivity: 94 },
  { month: 'Nov', efficiency: 87, satisfaction: 4.5, productivity: 93 },
  { month: 'Dec', efficiency: 88, satisfaction: 4.6, productivity: 92 },
];

export function PerformanceOverviewChart() {
  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Line 
            type="monotone" 
            dataKey="efficiency" 
            stroke="#3B82F6" 
            strokeWidth={2}
            name="Efficiency (%)"
          />
          <Line 
            type="monotone" 
            dataKey="satisfaction" 
            stroke="#10B981" 
            strokeWidth={2}
            name="Satisfaction (x20)"
            // Multiply by 20 to scale satisfaction (4.6 -> 92) for better visualization
          />
          <Line 
            type="monotone" 
            dataKey="productivity" 
            stroke="#F59E0B" 
            strokeWidth={2}
            name="Productivity (%)"
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
