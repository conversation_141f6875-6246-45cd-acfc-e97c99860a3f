"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

const data = [
  { month: 'Jan', newPatients: 65, returningPatients: 180 },
  { month: 'Feb', newPatients: 59, returningPatients: 195 },
  { month: 'Mar', newPatients: 80, returningPatients: 210 },
  { month: 'Apr', newPatients: 81, returningPatients: 225 },
  { month: 'May', newPatients: 56, returningPatients: 240 },
  { month: 'Jun', newPatients: 55, returningPatients: 255 },
  { month: 'Jul', newPatients: 40, returningPatients: 270 },
  { month: 'Aug', newPatients: 45, returningPatients: 285 },
  { month: 'Sep', newPatients: 60, returningPatients: 300 },
  { month: 'Oct', newPatients: 70, returningPatients: 315 },
  { month: 'Nov', newPatients: 85, returningPatients: 330 },
  { month: 'Dec', newPatients: 90, returningPatients: 345 },
];

export function PatientActivity<PERSON><PERSON>() {
  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Line 
            type="monotone" 
            dataKey="newPatients" 
            stroke="#10B981" 
            strokeWidth={2}
            name="New Patients"
          />
          <Line 
            type="monotone" 
            dataKey="returningPatients" 
            stroke="#3B82F6" 
            strokeWidth={2}
            name="Returning Patients"
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
