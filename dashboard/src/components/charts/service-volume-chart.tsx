"use client";

import { Line<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, ResponsiveC<PERSON><PERSON> } from 'recharts';

const data = [
  { date: '01/12', orders: 245 },
  { date: '02/12', orders: 267 },
  { date: '03/12', orders: 289 },
  { date: '04/12', orders: 312 },
  { date: '05/12', orders: 298 },
  { date: '06/12', orders: 276 },
  { date: '07/12', orders: 234 },
  { date: '08/12', orders: 287 },
  { date: '09/12', orders: 301 },
  { date: '10/12', orders: 324 },
  { date: '11/12', orders: 345 },
  { date: '12/12', orders: 367 },
  { date: '13/12', orders: 389 },
  { date: '14/12', orders: 356 },
  { date: '15/12', orders: 334 },
  { date: '16/12', orders: 312 },
  { date: '17/12', orders: 298 },
  { date: '18/12', orders: 276 },
  { date: '19/12', orders: 254 },
  { date: '20/12', orders: 287 },
  { date: '21/12', orders: 301 },
  { date: '22/12', orders: 324 },
  { date: '23/12', orders: 345 },
  { date: '24/12', orders: 367 },
  { date: '25/12', orders: 189 },
  { date: '26/12', orders: 234 },
  { date: '27/12', orders: 267 },
  { date: '28/12', orders: 289 },
  { date: '29/12', orders: 312 },
  { date: '30/12', orders: 334 },
];

export function ServiceVolumeChart() {
  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="date" 
            fontSize={12}
            tick={{ fontSize: 10 }}
          />
          <YAxis />
          <Tooltip />
          <Line 
            type="monotone" 
            dataKey="orders" 
            stroke="#3B82F6" 
            strokeWidth={2}
            dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
