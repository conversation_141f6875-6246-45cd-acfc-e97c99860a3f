"use client";

import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const data = [
  { hour: '07:00', avgTime: 6.2 },
  { hour: '08:00', avgTime: 8.5 },
  { hour: '09:00', avgTime: 9.8 },
  { hour: '10:00', avgTime: 7.3 },
  { hour: '11:00', avgTime: 6.9 },
  { hour: '12:00', avgTime: 5.4 },
  { hour: '13:00', avgTime: 4.8 },
  { hour: '14:00', avgTime: 8.9 },
  { hour: '15:00', avgTime: 10.2 },
  { hour: '16:00', avgTime: 9.1 },
  { hour: '17:00', avgTime: 7.6 },
  { hour: '18:00', avgTime: 6.3 },
];

export function ServiceTimeChart() {
  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="hour" 
            fontSize={12}
          />
          <YAxis 
            label={{ value: 'Minutes', angle: -90, position: 'insideLeft' }}
          />
          <Tooltip 
            formatter={(value) => [`${value} min`, 'Avg. Service Time']}
          />
          <Bar 
            dataKey="avgTime" 
            fill="#10B981"
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
