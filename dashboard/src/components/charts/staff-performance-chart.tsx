"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const data = [
  { name: '<PERSON>', score: 96 },
  { name: '<PERSON>', score: 94 },
  { name: '<PERSON>', score: 91 },
  { name: '<PERSON>', score: 89 },
  { name: '<PERSON>', score: 87 },
  { name: '<PERSON>', score: 85 },
  { name: '<PERSON><PERSON><PERSON>', score: 83 },
  { name: '<PERSON>', score: 81 },
];

export function StaffPerformanceChart() {
  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="name" 
            angle={-45}
            textAnchor="end"
            height={80}
            fontSize={12}
          />
          <YAxis 
            domain={[70, 100]}
            label={{ value: 'Performance Score', angle: -90, position: 'insideLeft' }}
          />
          <Tooltip 
            formatter={(value) => [`${value}%`, 'Performance Score']}
          />
          <Bar 
            dataKey="score" 
            fill="#8B5CF6"
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
