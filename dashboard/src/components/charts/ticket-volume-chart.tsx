"use client";

import { Composed<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

const data = [
  { date: '01/12', newTickets: 45, resolved: 42, pending: 8 },
  { date: '02/12', newTickets: 52, resolved: 48, pending: 12 },
  { date: '03/12', newTickets: 38, resolved: 41, pending: 9 },
  { date: '04/12', newTickets: 61, resolved: 55, pending: 15 },
  { date: '05/12', newTickets: 49, resolved: 52, pending: 12 },
  { date: '06/12', newTickets: 43, resolved: 46, pending: 9 },
  { date: '07/12', newTickets: 35, resolved: 38, pending: 6 },
  { date: '08/12', newTickets: 58, resolved: 54, pending: 10 },
  { date: '09/12', newTickets: 47, resolved: 49, pending: 8 },
  { date: '10/12', newTickets: 53, resolved: 51, pending: 10 },
  { date: '11/12', newTickets: 42, resolved: 45, pending: 7 },
  { date: '12/12', newTickets: 56, resolved: 53, pending: 10 },
  { date: '13/12', newTickets: 48, resolved: 50, pending: 8 },
  { date: '14/12', newTickets: 44, resolved: 47, pending: 5 },
  { date: '15/12', newTickets: 51, resolved: 48, pending: 8 },
];

export function TicketVolumeChart() {
  return (
    <div className="h-[400px]">
      <ResponsiveContainer width="100%" height="100%">
        <ComposedChart
          data={data}
          margin={{
            top: 20,
            right: 30,
            bottom: 20,
            left: 20,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="date" 
            fontSize={12}
          />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar 
            dataKey="newTickets" 
            fill="#3B82F6" 
            name="New Tickets"
            radius={[2, 2, 0, 0]}
          />
          <Bar 
            dataKey="resolved" 
            fill="#10B981" 
            name="Resolved"
            radius={[2, 2, 0, 0]}
          />
          <Area 
            type="monotone" 
            dataKey="pending" 
            fill="#F59E0B" 
            stroke="#F59E0B"
            name="Pending"
            fillOpacity={0.6}
          />
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
}
