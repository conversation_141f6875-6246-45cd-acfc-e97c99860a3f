"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const data = [
  { day: 'Mon', orders: 1245 },
  { day: 'Tue', orders: 1189 },
  { day: 'Wed', orders: 1067 },
  { day: 'Thu', orders: 1134 },
  { day: 'Fri', orders: 1298 },
  { day: 'Sat', orders: 856 },
  { day: 'Sun', orders: 234 },
];

export function ServiceWeekdayChart() {
  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="day" />
          <YAxis />
          <Tooltip 
            formatter={(value) => [`${value}`, 'Service Orders']}
          />
          <Bar 
            dataKey="orders" 
            fill="#8B5CF6"
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
