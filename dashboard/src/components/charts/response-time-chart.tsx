"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const data = [
  { channel: 'Phone', avgTime: 0.5, target: 1.0 },
  { channel: 'WhatsApp', avgTime: 1.2, target: 2.0 },
  { channel: 'Email', avgTime: 4.8, target: 6.0 },
  { channel: 'In-Person', avgTime: 0.1, target: 0.5 },
];

export function ResponseTimeChart() {
  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="channel" />
          <YAxis 
            label={{ value: 'Hours', angle: -90, position: 'insideLeft' }}
          />
          <Tooltip 
            formatter={(value, name) => [
              `${value}h`,
              name === 'avgTime' ? 'Avg Response Time' : 'Target'
            ]}
          />
          <Bar 
            dataKey="avgTime" 
            fill="#3B82F6"
            radius={[4, 4, 0, 0]}
            name="avgTime"
          />
          <Bar 
            dataKey="target" 
            fill="#E5E7EB"
            radius={[4, 4, 0, 0]}
            name="target"
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
