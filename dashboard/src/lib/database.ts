import * as odbc from 'odbc';

// Database configuration
const dbConfig = {
  host: process.env.IRIS_HOST || 'localhost',
  port: parseInt(process.env.IRIS_PORT || '1972'),
  namespace: process.env.IRIS_NAMESPACE || 'dado',
  user: process.env.IRIS_USER || '_SYSTEM',
  password: process.env.IRIS_PASSWORD || 'SYS',
  sslMode: parseInt(process.env.IRIS_SSL_MODE || '0'),
};

// Build connection string
function buildConnectionString() {
  const driverPath = process.env.IRIS_DRIVER_PATH;
  
  // If no driver path is specified or is "system", use system driver name
  const driverPart = (!driverPath || driverPath === 'system') 
    ? 'DRIVER={InterSystems ODBC35}'
    : `DRIVER=${driverPath}`;

  return `${driverPart};` +
         `SERVER=${dbConfig.host};` +
         `PORT=${dbConfig.port};` +
         `DATABASE=${dbConfig.namespace};` +
         `UID=${dbConfig.user};` +
         `PWD=${dbConfig.password};` +
         `SSL_MODE=${dbConfig.sslMode};`;
}

// Get database connection
export async function getConnection() {
  try {
    const connectionString = buildConnectionString();
    
    const connection = await odbc.connect({
      connectionString,
      connectionTimeout: 30,
      loginTimeout: 30,
    });
    
    console.log('Database connection established');
    return connection;
  } catch (error) {
    console.error('Database connection failed:', error);
    throw error;
  }
}

// Close database connection
export async function closeConnection(connection: any) {
  if (connection) {
    try {
      await connection.close();
      console.log('Database connection closed');
    } catch (error) {
      console.error('Error closing connection:', error);
    }
  }
}

// Execute query with connection management
export async function executeQuery<T = any>(query: string, params: any[] = []): Promise<T[]> {
  let connection = null;
  
  try {
    connection = await getConnection();
    const results = await connection.query(query, params);
    
    // Convert BigInt to string for JSON serialization
    const processedResults = results.map((row: any) => {
      const processedRow: any = {};
      for (const [key, value] of Object.entries(row)) {
        if (typeof value === 'bigint') {
          processedRow[key] = value.toString();
        } else {
          processedRow[key] = value;
        }
      }
      return processedRow;
    });
    
    return processedResults;
  } catch (error) {
    console.error('Query execution failed:', error);
    throw error;
  } finally {
    await closeConnection(connection);
  }
}

// Utility function to format WhatsApp number
export function formatWhatsAppNumber(telefone: string | null): string | null {
  if (!telefone) return null;

  // Clean number (remove special characters)
  const cleanNumber = telefone.replace(/\D/g, '');

  // Check if it has at least 10 digits (Brazilian format)
  if (cleanNumber.length < 10) return null;

  // Add country code if it doesn't have one (55 for Brazil)
  let formattedNumber = cleanNumber;
  if (!formattedNumber.startsWith('55') && formattedNumber.length <= 11) {
    formattedNumber = '55' + formattedNumber;
  }

  // Check if it's a valid number (12 or 13 digits with country code)
  if (formattedNumber.length === 13 || formattedNumber.length === 12) {
    return formattedNumber;
  }

  return null;
}

// Utility function to build date range filter
export function buildDateRangeFilter(startDate?: string, endDate?: string): string {
  if (!startDate && !endDate) return '';
  
  if (startDate && endDate) {
    return `WHERE OS.Data BETWEEN '${startDate}' AND '${endDate}'`;
  } else if (startDate) {
    return `WHERE OS.Data >= '${startDate}'`;
  } else if (endDate) {
    return `WHERE OS.Data <= '${endDate}'`;
  }
  
  return '';
}
