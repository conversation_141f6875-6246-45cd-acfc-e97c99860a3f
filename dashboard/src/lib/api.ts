// API client for dashboard data fetching

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '';

// Generic API fetch function with error handling
async function apiRequest<T>(endpoint: string, options?: RequestInit): Promise<T> {
  try {
    const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message || 'API request failed');
    }

    return data.data;
  } catch (error) {
    console.error(`API request error for ${endpoint}:`, error);
    throw error;
  }
}

// Patient Analytics API
export const patientApi = {
  async getAnalytics(params?: { startDate?: string; endDate?: string }) {
    const searchParams = new URLSearchParams();
    if (params?.startDate) searchParams.set('startDate', params.startDate);
    if (params?.endDate) searchParams.set('endDate', params.endDate);
    
    const query = searchParams.toString();
    return apiRequest(`/api/patients${query ? `?${query}` : ''}`);
  }
};

// Service Analytics API
export const serviceApi = {
  async getAnalytics(params?: { startDate?: string; endDate?: string }) {
    const searchParams = new URLSearchParams();
    if (params?.startDate) searchParams.set('startDate', params.startDate);
    if (params?.endDate) searchParams.set('endDate', params.endDate);
    
    const query = searchParams.toString();
    return apiRequest(`/api/services${query ? `?${query}` : ''}`);
  }
};

// Campaign API
export const campaignApi = {
  async getStats() {
    return apiRequest('/api/campaigns?action=stats');
  },

  async getCities() {
    return apiRequest('/api/campaigns?action=cities');
  },

  async getEligiblePatients(filters?: {
    city?: string;
    minAge?: number;
    maxAge?: number;
    lastVisitDays?: number;
    search?: string;
    limit?: number;
  }) {
    const searchParams = new URLSearchParams({ action: 'patients' });
    
    if (filters?.city) searchParams.set('city', filters.city);
    if (filters?.minAge) searchParams.set('minAge', filters.minAge.toString());
    if (filters?.maxAge) searchParams.set('maxAge', filters.maxAge.toString());
    if (filters?.lastVisitDays) searchParams.set('lastVisitDays', filters.lastVisitDays.toString());
    if (filters?.search) searchParams.set('search', filters.search);
    if (filters?.limit) searchParams.set('limit', filters.limit.toString());
    
    return apiRequest(`/api/campaigns?${searchParams.toString()}`);
  }
};

// Performance Analytics API
export const performanceApi = {
  async getAnalytics(params?: { startDate?: string; endDate?: string }) {
    const searchParams = new URLSearchParams();
    if (params?.startDate) searchParams.set('startDate', params.startDate);
    if (params?.endDate) searchParams.set('endDate', params.endDate);
    
    const query = searchParams.toString();
    return apiRequest(`/api/performance${query ? `?${query}` : ''}`);
  }
};

// Customer Service Analytics API
export const customerServiceApi = {
  async getAnalytics(params?: { startDate?: string; endDate?: string }) {
    const searchParams = new URLSearchParams();
    if (params?.startDate) searchParams.set('startDate', params.startDate);
    if (params?.endDate) searchParams.set('endDate', params.endDate);
    
    const query = searchParams.toString();
    return apiRequest(`/api/customer-service${query ? `?${query}` : ''}`);
  }
};

// Dashboard Overview API (combines multiple endpoints)
export const dashboardApi = {
  async getOverview() {
    try {
      const [patients, services, campaigns, performance] = await Promise.allSettled([
        patientApi.getAnalytics(),
        serviceApi.getAnalytics(),
        campaignApi.getStats(),
        performanceApi.getAnalytics()
      ]);

      return {
        patients: patients.status === 'fulfilled' ? patients.value : null,
        services: services.status === 'fulfilled' ? services.value : null,
        campaigns: campaigns.status === 'fulfilled' ? campaigns.value : null,
        performance: performance.status === 'fulfilled' ? performance.value : null,
        errors: [
          ...(patients.status === 'rejected' ? [{ section: 'patients', error: patients.reason }] : []),
          ...(services.status === 'rejected' ? [{ section: 'services', error: services.reason }] : []),
          ...(campaigns.status === 'rejected' ? [{ section: 'campaigns', error: campaigns.reason }] : []),
          ...(performance.status === 'rejected' ? [{ section: 'performance', error: performance.reason }] : [])
        ]
      };
    } catch (error) {
      console.error('Dashboard overview error:', error);
      throw error;
    }
  }
};

// Types for API responses
export interface PatientAnalytics {
  metrics: {
    activePatients: number;
    newPatients: number;
  };
  demographics: {
    age: Array<{ ageGroup: string; count: number }>;
    gender: Array<{ gender: string; count: number }>;
    location: Array<{ city: string; count: number }>;
  };
  topPatients: Array<{
    id: string;
    name: string;
    cpf: string;
    visitCount: number;
    lastVisit: string;
  }>;
  activityTrend: Array<{
    date: string;
    activePatients: number;
    totalOrders: number;
  }>;
}

export interface ServiceAnalytics {
  metrics: {
    totalOrders: number;
    averageServiceTime: number;
    reprintRate: number;
  };
  trends: {
    volume: Array<{ date: string; orders: number }>;
    timeDistribution: Array<{ hour: number; orders: number }>;
    weeklyPattern: Array<{ dayOfWeek: number; orders: number }>;
  };
  distribution: {
    serviceTypes: Array<{ type: string; count: number; percentage: number }>;
    onlineVsPresential: Array<{ type: string; count: number; percentage: number }>;
  };
  staff: Array<{
    id: string;
    orders: number;
    averageTime: number;
  }>;
}

export interface CampaignStats {
  stats: {
    totalEligible: number;
    lastWeek: number;
    lastMonth: number;
  };
  recentCampaigns: Array<{
    id: number;
    name: string;
    status: string;
    recipients: number;
    responses: number;
    responseRate: number;
    cost: number;
    createdAt: string;
  }>;
}

export interface EligiblePatients {
  patients: Array<{
    id: string;
    codigoOs: string;
    data: string;
    pacienteId: string;
    nome: string;
    email?: string;
    telefone: string;
    whatsapp: string;
    cidade?: string;
    idade?: number;
    totalVisitas: number;
  }>;
  total: number;
  filters: Record<string, any>;
}
