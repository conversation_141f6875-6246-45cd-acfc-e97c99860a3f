// Test database connection with real IRIS database
const BASE_URL = 'http://localhost:3001';

async function testAPI(endpoint, description) {
  try {
    console.log(`\n🔍 Testing ${description}...`);
    console.log(`   Endpoint: ${endpoint}`);
    
    const startTime = Date.now();
    const response = await fetch(`${BASE_URL}${endpoint}`);
    const endTime = Date.now();
    
    const data = await response.json();
    
    if (response.ok) {
      console.log(`✅ ${description} - SUCCESS`);
      console.log(`   Status: ${response.status}`);
      console.log(`   Response time: ${endTime - startTime}ms`);
      console.log(`   Data structure: ${Object.keys(data).join(', ')}`);
      
      // Show sample data for key fields
      if (data.totalPatients !== undefined) {
        console.log(`   Total Patients: ${data.totalPatients}`);
      }
      if (data.activePatients !== undefined) {
        console.log(`   Active Patients: ${data.activePatients}`);
      }
      if (data.demographics && data.demographics.length > 0) {
        console.log(`   Demographics records: ${data.demographics.length}`);
      }
      if (data.totalServices !== undefined) {
        console.log(`   Total Services: ${data.totalServices}`);
      }
      if (data.totalCampaigns !== undefined) {
        console.log(`   Total Campaigns: ${data.totalCampaigns}`);
      }
    } else {
      console.log(`❌ ${description} - ERROR`);
      console.log(`   Status: ${response.status}`);
      console.log(`   Error: ${data.error || 'Unknown error'}`);
      if (data.details) {
        console.log(`   Details: ${data.details}`);
      }
    }
  } catch (error) {
    console.log(`❌ ${description} - NETWORK ERROR`);
    console.log(`   Error: ${error.message}`);
  }
}

async function runDatabaseTests() {
  console.log('🚀 Testing IRIS Database Integration...\n');
  console.log('=' .repeat(60));
  
  await testAPI('/api/patients', 'Patient Analytics API');
  await testAPI('/api/services', 'Service Analytics API');
  await testAPI('/api/campaigns', 'Campaign Management API');
  await testAPI('/api/performance', 'Performance Analytics API');
  await testAPI('/api/customer-service', 'Customer Service API');
  
  console.log('\n' + '=' .repeat(60));
  console.log('🏁 Database integration tests completed.');
  console.log('\nIf all tests show SUCCESS, the dashboard is ready for production!');
  console.log('If any tests show ERROR, check the server logs for detailed error messages.');
}

runDatabaseTests();
