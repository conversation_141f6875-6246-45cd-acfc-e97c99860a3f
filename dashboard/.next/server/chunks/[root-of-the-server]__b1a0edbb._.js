module.exports = {

"[project]/.next-internal/server/app/api/campaigns/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/odbc [external] (odbc, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("odbc", () => require("odbc"));

module.exports = mod;
}}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "buildDateRangeFilter": (()=>buildDateRangeFilter),
    "closeConnection": (()=>closeConnection),
    "executeQuery": (()=>executeQuery),
    "formatWhatsAppNumber": (()=>formatWhatsAppNumber),
    "getConnection": (()=>getConnection)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$odbc__$5b$external$5d$__$28$odbc$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/odbc [external] (odbc, cjs)");
;
// Database configuration
const dbConfig = {
    host: process.env.IRIS_HOST || 'localhost',
    port: parseInt(process.env.IRIS_PORT || '1972'),
    namespace: process.env.IRIS_NAMESPACE || 'dado',
    user: process.env.IRIS_USER || '_SYSTEM',
    password: process.env.IRIS_PASSWORD || 'SYS',
    sslMode: parseInt(process.env.IRIS_SSL_MODE || '0')
};
// Build connection string
function buildConnectionString() {
    const driverPath = process.env.IRIS_DRIVER_PATH;
    // If no driver path is specified or is "system", use system driver name
    const driverPart = !driverPath || driverPath === 'system' ? 'DRIVER={InterSystems ODBC35}' : `DRIVER=${driverPath}`;
    return `${driverPart};` + `SERVER=${dbConfig.host};` + `PORT=${dbConfig.port};` + `DATABASE=${dbConfig.namespace};` + `UID=${dbConfig.user};` + `PWD=${dbConfig.password};` + `SSL_MODE=${dbConfig.sslMode};`;
}
async function getConnection() {
    try {
        const connectionString = buildConnectionString();
        const connection = await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$odbc__$5b$external$5d$__$28$odbc$2c$__cjs$29$__["connect"])({
            connectionString,
            connectionTimeout: 30,
            loginTimeout: 30
        });
        console.log('Database connection established');
        return connection;
    } catch (error) {
        console.error('Database connection failed:', error);
        throw error;
    }
}
async function closeConnection(connection) {
    if (connection) {
        try {
            await connection.close();
            console.log('Database connection closed');
        } catch (error) {
            console.error('Error closing connection:', error);
        }
    }
}
async function executeQuery(query, params = []) {
    let connection = null;
    try {
        connection = await getConnection();
        const results = await connection.query(query, params);
        // Convert BigInt to string for JSON serialization
        const processedResults = results.map((row)=>{
            const processedRow = {};
            for (const [key, value] of Object.entries(row)){
                if (typeof value === 'bigint') {
                    processedRow[key] = value.toString();
                } else {
                    processedRow[key] = value;
                }
            }
            return processedRow;
        });
        return processedResults;
    } catch (error) {
        console.error('Query execution failed:', error);
        throw error;
    } finally{
        await closeConnection(connection);
    }
}
function formatWhatsAppNumber(telefone) {
    if (!telefone) return null;
    // Clean number (remove special characters)
    const cleanNumber = telefone.replace(/\D/g, '');
    // Check if it has at least 10 digits (Brazilian format)
    if (cleanNumber.length < 10) return null;
    // Add country code if it doesn't have one (55 for Brazil)
    let formattedNumber = cleanNumber;
    if (!formattedNumber.startsWith('55') && formattedNumber.length <= 11) {
        formattedNumber = '55' + formattedNumber;
    }
    // Check if it's a valid number (12 or 13 digits with country code)
    if (formattedNumber.length === 13 || formattedNumber.length === 12) {
        return formattedNumber;
    }
    return null;
}
function buildDateRangeFilter(startDate, endDate) {
    if (!startDate && !endDate) return '';
    if (startDate && endDate) {
        return `WHERE OS.Data BETWEEN '${startDate}' AND '${endDate}'`;
    } else if (startDate) {
        return `WHERE OS.Data >= '${startDate}'`;
    } else if (endDate) {
        return `WHERE OS.Data <= '${endDate}'`;
    }
    return '';
}
}}),
"[project]/src/app/api/campaigns/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
// Campaign and patient selection queries
const CAMPAIGN_QUERIES = {
    // Get patients for WhatsApp campaigns with filters
    eligiblePatients: `
    SELECT
      OS.ID,
      OS.CodigoOs,
      OS.Data,
      OS.HoraInicial,
      OS.Paciente,
      PF.Nome,
      PF.Email,
      PF.TelefoneDdd,
      PF.TelefoneNumero,
      PF.EnderecoCidade,
      PF.DataNascimento,
      COUNT(*) OVER (PARTITION BY OS.Paciente) AS TotalVisitas
    FROM dado.ArqOrdemServico OS
    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID
    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND PF.TelefoneNumero IS NOT NULL
      AND PF.TelefoneNumero != ''
      AND PF.Nome IS NOT NULL
  `,
    // Campaign statistics
    campaignStats: `
    SELECT
      COUNT(DISTINCT OS.Paciente) AS TotalPacientesElegiveis,
      COUNT(DISTINCT CASE WHEN OS.Data >= DATEADD('day', -7, CURRENT_DATE) THEN OS.Paciente END) AS PacientesUltimaSemana,
      COUNT(DISTINCT CASE WHEN OS.Data >= DATEADD('day', -30, CURRENT_DATE) THEN OS.Paciente END) AS PacientesUltimoMes
    FROM dado.ArqOrdemServico OS
    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID
    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID
    WHERE PF.TelefoneNumero IS NOT NULL
      AND PF.TelefoneNumero != ''
  `,
    // Cities for filtering
    availableCities: `
    SELECT DISTINCT
      PF.EnderecoCidade AS Cidade,
      COUNT(*) AS QtdePacientes
    FROM dado.ArqOrdemServico OS
    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID
    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID
    WHERE OS.Data >= DATEADD('day', -90, CURRENT_DATE)
      AND PF.EnderecoCidade IS NOT NULL
      AND PF.EnderecoCidade != ''
    GROUP BY PF.EnderecoCidade
    ORDER BY QtdePacientes DESC
  `
};
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const action = searchParams.get('action') || 'stats';
        if (action === 'patients') {
            return await getEligiblePatients(searchParams);
        } else if (action === 'cities') {
            return await getAvailableCities();
        } else {
            return await getCampaignStats();
        }
    } catch (error) {
        console.error('Error in campaigns API:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to fetch campaign data',
            message: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
async function getCampaignStats() {
    const [campaignStats] = await Promise.all([
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(CAMPAIGN_QUERIES.campaignStats)
    ]);
    const response = {
        success: true,
        timestamp: new Date().toISOString(),
        data: {
            stats: {
                totalEligible: campaignStats[0]?.TotalPacientesElegiveis || 0,
                lastWeek: campaignStats[0]?.PacientesUltimaSemana || 0,
                lastMonth: campaignStats[0]?.PacientesUltimoMes || 0
            },
            // Mock campaign history for now
            recentCampaigns: [
                {
                    id: 1,
                    name: "Pesquisa de Satisfação - Dezembro",
                    status: "completed",
                    recipients: 245,
                    responses: 89,
                    responseRate: 36.3,
                    cost: 12.25,
                    createdAt: "2024-12-15T10:00:00Z"
                },
                {
                    id: 2,
                    name: "Lembrete de Retorno",
                    status: "active",
                    recipients: 156,
                    responses: 23,
                    responseRate: 14.7,
                    cost: 7.80,
                    createdAt: "2024-12-20T14:30:00Z"
                },
                {
                    id: 3,
                    name: "Promoção Exames Preventivos",
                    status: "scheduled",
                    recipients: 320,
                    responses: 0,
                    responseRate: 0,
                    cost: 16.00,
                    createdAt: "2024-12-22T09:00:00Z"
                }
            ]
        }
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response);
}
async function getAvailableCities() {
    const cities = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(CAMPAIGN_QUERIES.availableCities);
    const response = {
        success: true,
        timestamp: new Date().toISOString(),
        data: {
            cities: cities.map((row)=>({
                    name: row.Cidade,
                    patientCount: parseInt(row.QtdePacientes)
                }))
        }
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response);
}
async function getEligiblePatients(searchParams) {
    // Get filter parameters
    const city = searchParams.get('city');
    const minAge = searchParams.get('minAge');
    const maxAge = searchParams.get('maxAge');
    const lastVisitDays = searchParams.get('lastVisitDays') || '30';
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit') || '100');
    // Build dynamic query with filters
    let query = CAMPAIGN_QUERIES.eligiblePatients;
    const conditions = [];
    if (city && city !== 'all') {
        conditions.push(`PF.EnderecoCidade = '${city}'`);
    }
    if (minAge) {
        conditions.push(`DATEDIFF('year', PF.DataNascimento, CURRENT_DATE) >= ${minAge}`);
    }
    if (maxAge) {
        conditions.push(`DATEDIFF('year', PF.DataNascimento, CURRENT_DATE) <= ${maxAge}`);
    }
    if ("TURBOPACK compile-time truthy", 1) {
        conditions.push(`OS.Data >= DATEADD('day', -${lastVisitDays}, CURRENT_DATE)`);
    }
    if (search) {
        conditions.push(`(PF.Nome LIKE '%${search}%' OR PF.TelefoneNumero LIKE '%${search}%')`);
    }
    if (conditions.length > 0) {
        query += ' AND ' + conditions.join(' AND ');
    }
    query += ` ORDER BY OS.Data DESC FETCH FIRST ${limit} ROWS ONLY`;
    const patients = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query);
    // Process patients and format WhatsApp numbers
    const processedPatients = patients.map((row)=>{
        const telefoneCompleto = row.TelefoneDdd && row.TelefoneNumero ? `${row.TelefoneDdd}${row.TelefoneNumero}` : row.TelefoneNumero;
        const whatsappNumber = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["formatWhatsAppNumber"])(telefoneCompleto);
        const age = row.DataNascimento ? new Date().getFullYear() - new Date(row.DataNascimento).getFullYear() : null;
        return {
            id: row.ID,
            codigoOs: row.CodigoOs,
            data: row.Data,
            pacienteId: row.Paciente,
            nome: row.Nome,
            email: row.Email?.trim().toLowerCase(),
            telefone: telefoneCompleto?.trim(),
            whatsapp: whatsappNumber,
            cidade: row.EnderecoCidade,
            idade: age,
            totalVisitas: parseInt(row.TotalVisitas)
        };
    }).filter((patient)=>patient.whatsapp); // Only include patients with valid WhatsApp
    const response = {
        success: true,
        timestamp: new Date().toISOString(),
        data: {
            patients: processedPatients,
            total: processedPatients.length,
            filters: {
                city,
                minAge,
                maxAge,
                lastVisitDays,
                search,
                limit
            }
        }
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response);
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__b1a0edbb._.js.map