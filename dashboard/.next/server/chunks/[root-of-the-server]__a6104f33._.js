module.exports = {

"[project]/.next-internal/server/app/api/patients/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/odbc [external] (odbc, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("odbc", () => require("odbc"));

module.exports = mod;
}}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "buildDateRangeFilter": (()=>buildDateRangeFilter),
    "closeConnection": (()=>closeConnection),
    "executeQuery": (()=>executeQuery),
    "formatWhatsAppNumber": (()=>formatWhatsAppNumber),
    "getConnection": (()=>getConnection)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$odbc__$5b$external$5d$__$28$odbc$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/odbc [external] (odbc, cjs)");
;
// Database configuration
const dbConfig = {
    host: process.env.IRIS_HOST || 'localhost',
    port: parseInt(process.env.IRIS_PORT || '1972'),
    namespace: process.env.IRIS_NAMESPACE || 'dado',
    user: process.env.IRIS_USER || '_SYSTEM',
    password: process.env.IRIS_PASSWORD || 'SYS',
    sslMode: parseInt(process.env.IRIS_SSL_MODE || '0')
};
// Build connection string
function buildConnectionString() {
    const driverPath = process.env.IRIS_DRIVER_PATH;
    // If no driver path is specified or is "system", use system driver name
    const driverPart = !driverPath || driverPath === 'system' ? 'DRIVER={InterSystems ODBC35}' : `DRIVER=${driverPath}`;
    return `${driverPart};` + `SERVER=${dbConfig.host};` + `PORT=${dbConfig.port};` + `DATABASE=${dbConfig.namespace};` + `UID=${dbConfig.user};` + `PWD=${dbConfig.password};` + `SSL_MODE=${dbConfig.sslMode};`;
}
async function getConnection() {
    try {
        const connectionString = buildConnectionString();
        const connection = await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$odbc__$5b$external$5d$__$28$odbc$2c$__cjs$29$__["connect"])({
            connectionString,
            connectionTimeout: 30,
            loginTimeout: 30
        });
        console.log('Database connection established');
        return connection;
    } catch (error) {
        console.error('Database connection failed:', error);
        throw error;
    }
}
async function closeConnection(connection) {
    if (connection) {
        try {
            await connection.close();
            console.log('Database connection closed');
        } catch (error) {
            console.error('Error closing connection:', error);
        }
    }
}
async function executeQuery(query, params = []) {
    let connection = null;
    try {
        connection = await getConnection();
        const results = await connection.query(query, params);
        // Convert BigInt to string for JSON serialization
        const processedResults = results.map((row)=>{
            const processedRow = {};
            for (const [key, value] of Object.entries(row)){
                if (typeof value === 'bigint') {
                    processedRow[key] = value.toString();
                } else {
                    processedRow[key] = value;
                }
            }
            return processedRow;
        });
        return processedResults;
    } catch (error) {
        console.error('Query execution failed:', error);
        throw error;
    } finally{
        await closeConnection(connection);
    }
}
function formatWhatsAppNumber(telefone) {
    if (!telefone) return null;
    // Clean number (remove special characters)
    const cleanNumber = telefone.replace(/\D/g, '');
    // Check if it has at least 10 digits (Brazilian format)
    if (cleanNumber.length < 10) return null;
    // Add country code if it doesn't have one (55 for Brazil)
    let formattedNumber = cleanNumber;
    if (!formattedNumber.startsWith('55') && formattedNumber.length <= 11) {
        formattedNumber = '55' + formattedNumber;
    }
    // Check if it's a valid number (12 or 13 digits with country code)
    if (formattedNumber.length === 13 || formattedNumber.length === 12) {
        return formattedNumber;
    }
    return null;
}
function buildDateRangeFilter(startDate, endDate) {
    if (!startDate && !endDate) return '';
    if (startDate && endDate) {
        return `WHERE OS.Data BETWEEN '${startDate}' AND '${endDate}'`;
    } else if (startDate) {
        return `WHERE OS.Data >= '${startDate}'`;
    } else if (endDate) {
        return `WHERE OS.Data <= '${endDate}'`;
    }
    return '';
}
}}),
"[project]/src/app/api/patients/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
// Patient analytics queries
const PATIENT_QUERIES = {
    // Active patients count
    activePatients: `
    SELECT COUNT(DISTINCT OS.Paciente) AS PacientesAtivos
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
  `,
    // New patients in period
    newPatients: `
    SELECT COUNT(*) AS NovosPacientes
    FROM dado.ArqPaciente P
    WHERE P.PrimeiraOS >= DATEADD('day', -30, CURRENT_DATE)
  `,
    // Patient age distribution
    ageDistribution: `
    SELECT
      CASE
        WHEN DATEDIFF('year', PF.DataNascimento, CURRENT_DATE) < 18 THEN '<18'
        WHEN DATEDIFF('year', PF.DataNascimento, CURRENT_DATE) BETWEEN 18 AND 30 THEN '18-30'
        WHEN DATEDIFF('year', PF.DataNascimento, CURRENT_DATE) BETWEEN 31 AND 50 THEN '31-50'
        WHEN DATEDIFF('year', PF.DataNascimento, CURRENT_DATE) BETWEEN 51 AND 65 THEN '51-65'
        ELSE '65+' 
      END AS FaixaIdade,
      COUNT(DISTINCT AP.ID) AS QtdePacientes
    FROM dado.TblPessoaFisica PF
    JOIN dado.ArqPaciente AP ON AP.PessoaFisica = PF.ID
    JOIN dado.ArqOrdemServico OS ON OS.Paciente = AP.ID
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
    GROUP BY 1
    ORDER BY FaixaIdade
  `,
    // Gender distribution
    genderDistribution: `
    SELECT
      PF.Sexo,
      COUNT(DISTINCT AP.ID) AS QtdePacientes
    FROM dado.TblPessoaFisica PF
    JOIN dado.ArqPaciente AP ON AP.PessoaFisica = PF.ID
    JOIN dado.ArqOrdemServico OS ON OS.Paciente = AP.ID
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
    GROUP BY PF.Sexo
  `,
    // Geographic distribution
    locationDistribution: `
    SELECT
      PF.EnderecoCidade AS Cidade,
      COUNT(DISTINCT AP.ID) AS QtdePacientes
    FROM dado.TblPessoaFisica PF
    JOIN dado.ArqPaciente AP ON AP.PessoaFisica = PF.ID
    JOIN dado.ArqOrdemServico OS ON OS.Paciente = AP.ID
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND PF.EnderecoCidade IS NOT NULL
    GROUP BY PF.EnderecoCidade
    ORDER BY QtdePacientes DESC
    FETCH FIRST 10 ROWS ONLY
  `,
    // Top patients by visit frequency
    topPatients: `
    SELECT
      AP.ID AS PacienteID,
      PF.Nome,
      PF.Cpf,
      COUNT(*) AS QtdeOS,
      MAX(OS.Data) AS UltimaVisita
    FROM dado.ArqOrdemServico OS
    JOIN dado.ArqPaciente AP ON OS.Paciente = AP.ID
    JOIN dado.TblPessoaFisica PF ON AP.PessoaFisica = PF.ID
    WHERE OS.Data >= DATEADD('day', -90, CURRENT_DATE)
    GROUP BY AP.ID, PF.Nome, PF.Cpf
    ORDER BY QtdeOS DESC
    FETCH FIRST 10 ROWS ONLY
  `,
    // Patient activity trend (last 30 days)
    activityTrend: `
    SELECT
      OS.Data,
      COUNT(DISTINCT OS.Paciente) AS PacientesAtivos,
      COUNT(*) AS TotalOS
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
    GROUP BY OS.Data
    ORDER BY OS.Data
  `
};
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        // Execute all patient analytics queries
        const [activePatients, newPatients, ageDistribution, genderDistribution, locationDistribution, topPatients, activityTrend] = await Promise.all([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(PATIENT_QUERIES.activePatients),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(PATIENT_QUERIES.newPatients),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(PATIENT_QUERIES.ageDistribution),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(PATIENT_QUERIES.genderDistribution),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(PATIENT_QUERIES.locationDistribution),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(PATIENT_QUERIES.topPatients),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(PATIENT_QUERIES.activityTrend)
        ]);
        // Format the response
        const response = {
            success: true,
            timestamp: new Date().toISOString(),
            data: {
                metrics: {
                    activePatients: activePatients[0]?.PacientesAtivos || 0,
                    newPatients: newPatients[0]?.NovosPacientes || 0
                },
                demographics: {
                    age: ageDistribution.map((row)=>({
                            ageGroup: row.FaixaIdade,
                            count: parseInt(row.QtdePacientes)
                        })),
                    gender: genderDistribution.map((row)=>({
                            gender: row.Sexo,
                            count: parseInt(row.QtdePacientes)
                        })),
                    location: locationDistribution.map((row)=>({
                            city: row.Cidade,
                            count: parseInt(row.QtdePacientes)
                        }))
                },
                topPatients: topPatients.map((row)=>({
                        id: row.PacienteID,
                        name: row.Nome,
                        cpf: row.Cpf,
                        visitCount: parseInt(row.QtdeOS),
                        lastVisit: row.UltimaVisita
                    })),
                activityTrend: activityTrend.map((row)=>({
                        date: row.Data,
                        activePatients: parseInt(row.PacientesAtivos),
                        totalOrders: parseInt(row.TotalOS)
                    }))
            }
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response);
    } catch (error) {
        console.error('Error fetching patient analytics:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to fetch patient analytics',
            message: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__a6104f33._.js.map