{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/lib/database.ts"], "sourcesContent": ["import * as odbc from 'odbc';\n\n// Database configuration\nconst dbConfig = {\n  host: process.env.IRIS_HOST || 'localhost',\n  port: parseInt(process.env.IRIS_PORT || '1972'),\n  namespace: process.env.IRIS_NAMESPACE || 'dado',\n  user: process.env.IRIS_USER || '_SYSTEM',\n  password: process.env.IRIS_PASSWORD || 'SYS',\n  sslMode: parseInt(process.env.IRIS_SSL_MODE || '0'),\n};\n\n// Build connection string\nfunction buildConnectionString() {\n  const driverPath = process.env.IRIS_DRIVER_PATH;\n  \n  // If no driver path is specified or is \"system\", use system driver name\n  const driverPart = (!driverPath || driverPath === 'system') \n    ? 'DRIVER={InterSystems ODBC35}'\n    : `DRIVER=${driverPath}`;\n\n  return `${driverPart};` +\n         `SERVER=${dbConfig.host};` +\n         `PORT=${dbConfig.port};` +\n         `DATABASE=${dbConfig.namespace};` +\n         `UID=${dbConfig.user};` +\n         `PWD=${dbConfig.password};` +\n         `SSL_MODE=${dbConfig.sslMode};`;\n}\n\n// Get database connection\nexport async function getConnection() {\n  try {\n    const connectionString = buildConnectionString();\n    \n    const connection = await odbc.connect({\n      connectionString,\n      connectionTimeout: 30,\n      loginTimeout: 30,\n    });\n    \n    console.log('Database connection established');\n    return connection;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    throw error;\n  }\n}\n\n// Close database connection\nexport async function closeConnection(connection: any) {\n  if (connection) {\n    try {\n      await connection.close();\n      console.log('Database connection closed');\n    } catch (error) {\n      console.error('Error closing connection:', error);\n    }\n  }\n}\n\n// Execute query with connection management\nexport async function executeQuery<T = any>(query: string, params: any[] = []): Promise<T[]> {\n  let connection = null;\n  \n  try {\n    connection = await getConnection();\n    const results = await connection.query(query, params);\n    \n    // Convert BigInt to string for JSON serialization\n    const processedResults = results.map((row: any) => {\n      const processedRow: any = {};\n      for (const [key, value] of Object.entries(row)) {\n        if (typeof value === 'bigint') {\n          processedRow[key] = value.toString();\n        } else {\n          processedRow[key] = value;\n        }\n      }\n      return processedRow;\n    });\n    \n    return processedResults;\n  } catch (error) {\n    console.error('Query execution failed:', error);\n    throw error;\n  } finally {\n    await closeConnection(connection);\n  }\n}\n\n// Utility function to format WhatsApp number\nexport function formatWhatsAppNumber(telefone: string | null): string | null {\n  if (!telefone) return null;\n\n  // Clean number (remove special characters)\n  const cleanNumber = telefone.replace(/\\D/g, '');\n\n  // Check if it has at least 10 digits (Brazilian format)\n  if (cleanNumber.length < 10) return null;\n\n  // Add country code if it doesn't have one (55 for Brazil)\n  let formattedNumber = cleanNumber;\n  if (!formattedNumber.startsWith('55') && formattedNumber.length <= 11) {\n    formattedNumber = '55' + formattedNumber;\n  }\n\n  // Check if it's a valid number (12 or 13 digits with country code)\n  if (formattedNumber.length === 13 || formattedNumber.length === 12) {\n    return formattedNumber;\n  }\n\n  return null;\n}\n\n// Utility function to build date range filter\nexport function buildDateRangeFilter(startDate?: string, endDate?: string): string {\n  if (!startDate && !endDate) return '';\n  \n  if (startDate && endDate) {\n    return `WHERE OS.Data BETWEEN '${startDate}' AND '${endDate}'`;\n  } else if (startDate) {\n    return `WHERE OS.Data >= '${startDate}'`;\n  } else if (endDate) {\n    return `WHERE OS.Data <= '${endDate}'`;\n  }\n  \n  return '';\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA,yBAAyB;AACzB,MAAM,WAAW;IACf,MAAM,QAAQ,GAAG,CAAC,SAAS,IAAI;IAC/B,MAAM,SAAS,QAAQ,GAAG,CAAC,SAAS,IAAI;IACxC,WAAW,QAAQ,GAAG,CAAC,cAAc,IAAI;IACzC,MAAM,QAAQ,GAAG,CAAC,SAAS,IAAI;IAC/B,UAAU,QAAQ,GAAG,CAAC,aAAa,IAAI;IACvC,SAAS,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;AACjD;AAEA,0BAA0B;AAC1B,SAAS;IACP,MAAM,aAAa,QAAQ,GAAG,CAAC,gBAAgB;IAE/C,wEAAwE;IACxE,MAAM,aAAa,AAAC,CAAC,cAAc,eAAe,WAC9C,iCACA,CAAC,OAAO,EAAE,YAAY;IAE1B,OAAO,GAAG,WAAW,CAAC,CAAC,GAChB,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,GAC1B,CAAC,KAAK,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,GACxB,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC,CAAC,CAAC,GACjC,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,GACvB,CAAC,IAAI,EAAE,SAAS,QAAQ,CAAC,CAAC,CAAC,GAC3B,CAAC,SAAS,EAAE,SAAS,OAAO,CAAC,CAAC,CAAC;AACxC;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,mBAAmB;QAEzB,MAAM,aAAa,MAAM,CAAA,GAAA,iGAAA,CAAA,UAAY,AAAD,EAAE;YACpC;YACA,mBAAmB;YACnB,cAAc;QAChB;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAGO,eAAe,gBAAgB,UAAe;IACnD,IAAI,YAAY;QACd,IAAI;YACF,MAAM,WAAW,KAAK;YACtB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;AACF;AAGO,eAAe,aAAsB,KAAa,EAAE,SAAgB,EAAE;IAC3E,IAAI,aAAa;IAEjB,IAAI;QACF,aAAa,MAAM;QACnB,MAAM,UAAU,MAAM,WAAW,KAAK,CAAC,OAAO;QAE9C,kDAAkD;QAClD,MAAM,mBAAmB,QAAQ,GAAG,CAAC,CAAC;YACpC,MAAM,eAAoB,CAAC;YAC3B,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAM;gBAC9C,IAAI,OAAO,UAAU,UAAU;oBAC7B,YAAY,CAAC,IAAI,GAAG,MAAM,QAAQ;gBACpC,OAAO;oBACL,YAAY,CAAC,IAAI,GAAG;gBACtB;YACF;YACA,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR,SAAU;QACR,MAAM,gBAAgB;IACxB;AACF;AAGO,SAAS,qBAAqB,QAAuB;IAC1D,IAAI,CAAC,UAAU,OAAO;IAEtB,2CAA2C;IAC3C,MAAM,cAAc,SAAS,OAAO,CAAC,OAAO;IAE5C,wDAAwD;IACxD,IAAI,YAAY,MAAM,GAAG,IAAI,OAAO;IAEpC,0DAA0D;IAC1D,IAAI,kBAAkB;IACtB,IAAI,CAAC,gBAAgB,UAAU,CAAC,SAAS,gBAAgB,MAAM,IAAI,IAAI;QACrE,kBAAkB,OAAO;IAC3B;IAEA,mEAAmE;IACnE,IAAI,gBAAgB,MAAM,KAAK,MAAM,gBAAgB,MAAM,KAAK,IAAI;QAClE,OAAO;IACT;IAEA,OAAO;AACT;AAGO,SAAS,qBAAqB,SAAkB,EAAE,OAAgB;IACvE,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;IAEnC,IAAI,aAAa,SAAS;QACxB,OAAO,CAAC,uBAAuB,EAAE,UAAU,OAAO,EAAE,QAAQ,CAAC,CAAC;IAChE,OAAO,IAAI,WAAW;QACpB,OAAO,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;IAC1C,OAAO,IAAI,SAAS;QAClB,OAAO,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;IACxC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/app/api/performance/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { executeQuery } from '@/lib/database';\n\n// Performance analytics queries\nconst PERFORMANCE_QUERIES = {\n  // Overall efficiency metrics\n  overallMetrics: `\n    SELECT\n      COUNT(*) AS TotalOS,\n      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS TempoMedioMin,\n      COUNT(CASE WHEN OS.ReimpressaoData IS NULL THEN 1 END) * 100.0 / COUNT(*) AS EficienciaPerc,\n      COUNT(DISTINCT OS.Paciente) AS PacientesAtendidos\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)\n      AND OS.HoraInicial IS NOT NULL \n      AND OS.HoraFinal IS NOT NULL\n  `,\n\n  // Staff performance\n  staffPerformance: `\n    SELECT\n      OS.Recepcionista AS StaffId,\n      COUNT(*) AS QtdeOS,\n      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS TempoMedioMin,\n      COUNT(CASE WHEN OS.ReimpressaoData IS NULL THEN 1 END) * 100.0 / COUNT(*) AS EficienciaPerc\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)\n      AND OS.Recepcionista IS NOT NULL\n      AND OS.HoraInicial IS NOT NULL \n      AND OS.HoraFinal IS NOT NULL\n    GROUP BY OS.Recepcionista\n    ORDER BY QtdeOS DESC\n  `,\n\n  // Performance trend over time (last 6 months)\n  performanceTrend: `\n    SELECT\n      YEAR(OS.Data) AS Ano,\n      MONTH(OS.Data) AS Mes,\n      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS TempoMedioMin,\n      COUNT(CASE WHEN OS.ReimpressaoData IS NULL THEN 1 END) * 100.0 / COUNT(*) AS EficienciaPerc,\n      COUNT(DISTINCT OS.Paciente) AS PacientesAtendidos\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= DATEADD('month', -6, CURRENT_DATE)\n      AND OS.HoraInicial IS NOT NULL \n      AND OS.HoraFinal IS NOT NULL\n    GROUP BY YEAR(OS.Data), MONTH(OS.Data)\n    ORDER BY Ano, Mes\n  `,\n\n  // Daily efficiency trend (last 15 days)\n  dailyEfficiency: `\n    SELECT\n      OS.Data,\n      COUNT(*) AS QtdeOS,\n      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS TempoMedioMin,\n      COUNT(CASE WHEN OS.ReimpressaoData IS NULL THEN 1 END) * 100.0 / COUNT(*) AS EficienciaPerc\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= DATEADD('day', -15, CURRENT_DATE)\n      AND OS.HoraInicial IS NOT NULL \n      AND OS.HoraFinal IS NOT NULL\n    GROUP BY OS.Data\n    ORDER BY OS.Data\n  `,\n\n  // Service quality metrics\n  qualityMetrics: `\n    SELECT\n      COUNT(CASE WHEN OS.ReimpressaoData IS NULL THEN 1 END) * 100.0 / COUNT(*) AS PrimeiraResolucaoPerc,\n      COUNT(CASE WHEN OS.OsStatus = 1 THEN 1 END) * 100.0 / COUNT(*) AS TaxaSucessoPerc,\n      COUNT(CASE WHEN OS.OsStatus = 0 THEN 1 END) * 100.0 / COUNT(*) AS TaxaCancelamentoPerc\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)\n  `\n};\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const startDate = searchParams.get('startDate');\n    const endDate = searchParams.get('endDate');\n\n    // Execute all performance analytics queries\n    const [\n      overallMetrics,\n      staffPerformance,\n      performanceTrend,\n      dailyEfficiency,\n      qualityMetrics\n    ] = await Promise.all([\n      executeQuery(PERFORMANCE_QUERIES.overallMetrics),\n      executeQuery(PERFORMANCE_QUERIES.staffPerformance),\n      executeQuery(PERFORMANCE_QUERIES.performanceTrend),\n      executeQuery(PERFORMANCE_QUERIES.dailyEfficiency),\n      executeQuery(PERFORMANCE_QUERIES.qualityMetrics)\n    ]);\n\n    // Calculate overall efficiency score\n    const efficiency = overallMetrics[0]?.EficienciaPerc || 0;\n    const avgServiceTime = overallMetrics[0]?.TempoMedioMin || 0;\n    const qualityScore = qualityMetrics[0]?.PrimeiraResolucaoPerc || 0;\n    \n    // Mock patient satisfaction (would come from a separate survey system)\n    const patientSatisfaction = 4.6;\n    const staffProductivity = Math.min(100, Math.max(0, 100 - (avgServiceTime - 10) * 2));\n\n    // Format the response\n    const response = {\n      success: true,\n      timestamp: new Date().toISOString(),\n      data: {\n        metrics: {\n          overallEfficiency: parseFloat(efficiency.toFixed(1)),\n          patientSatisfaction: patientSatisfaction,\n          staffProductivity: parseFloat(staffProductivity.toFixed(1)),\n          averageWaitTime: Math.round(avgServiceTime),\n          totalPatients: overallMetrics[0]?.PacientesAtendidos || 0,\n          totalOrders: overallMetrics[0]?.TotalOS || 0\n        },\n        quality: {\n          firstTimeResolution: parseFloat((qualityMetrics[0]?.PrimeiraResolucaoPerc || 0).toFixed(1)),\n          successRate: parseFloat((qualityMetrics[0]?.TaxaSucessoPerc || 0).toFixed(1)),\n          cancellationRate: parseFloat((qualityMetrics[0]?.TaxaCancelamentoPerc || 0).toFixed(1))\n        },\n        trends: {\n          performance: performanceTrend.map(row => ({\n            month: `${row.Ano}-${String(row.Mes).padStart(2, '0')}`,\n            efficiency: parseFloat((row.EficienciaPerc || 0).toFixed(1)),\n            averageTime: Math.round(row.TempoMedioMin || 0),\n            patients: parseInt(row.PacientesAtendidos)\n          })),\n          daily: dailyEfficiency.map(row => ({\n            date: row.Data,\n            efficiency: parseFloat((row.EficienciaPerc || 0).toFixed(1)),\n            orders: parseInt(row.QtdeOS),\n            averageTime: Math.round(row.TempoMedioMin || 0)\n          }))\n        },\n        staff: staffPerformance.map((row, index) => {\n          // Mock staff names for demo\n          const staffNames = [\n            'Ana Silva', 'Carlos Santos', 'Maria Costa', 'João Oliveira',\n            'Paula Lima', 'Roberto Silva', 'Fernanda Cruz', 'Lucas Pereira'\n          ];\n          \n          const performanceScore = Math.min(100, Math.max(0, \n            (row.EficienciaPerc || 0) * 0.6 + \n            Math.max(0, 100 - (row.TempoMedioMin - 10) * 2) * 0.4\n          ));\n\n          return {\n            id: row.StaffId,\n            name: staffNames[index] || `Staff ${row.StaffId}`,\n            orders: parseInt(row.QtdeOS),\n            averageTime: Math.round(row.TempoMedioMin || 0),\n            efficiency: parseFloat((row.EficienciaPerc || 0).toFixed(1)),\n            performanceScore: Math.round(performanceScore)\n          };\n        }).slice(0, 8), // Top 8 performers\n        operational: {\n          resourceUtilization: 88,\n          equipmentUptime: 95,\n          processAutomation: 72\n        }\n      }\n    };\n\n    return NextResponse.json(response);\n\n  } catch (error) {\n    console.error('Error fetching performance analytics:', error);\n    return NextResponse.json(\n      {\n        success: false,\n        error: 'Failed to fetch performance analytics',\n        message: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,gCAAgC;AAChC,MAAM,sBAAsB;IAC1B,6BAA6B;IAC7B,gBAAgB,CAAC;;;;;;;;;;EAUjB,CAAC;IAED,oBAAoB;IACpB,kBAAkB,CAAC;;;;;;;;;;;;;EAanB,CAAC;IAED,8CAA8C;IAC9C,kBAAkB,CAAC;;;;;;;;;;;;;EAanB,CAAC;IAED,wCAAwC;IACxC,iBAAiB,CAAC;;;;;;;;;;;;EAYlB,CAAC;IAED,0BAA0B;IAC1B,gBAAgB,CAAC;;;;;;;EAOjB,CAAC;AACH;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,UAAU,aAAa,GAAG,CAAC;QAEjC,4CAA4C;QAC5C,MAAM,CACJ,gBACA,kBACA,kBACA,iBACA,eACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,oBAAoB,cAAc;YAC/C,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,oBAAoB,gBAAgB;YACjD,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,oBAAoB,gBAAgB;YACjD,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,oBAAoB,eAAe;YAChD,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,oBAAoB,cAAc;SAChD;QAED,qCAAqC;QACrC,MAAM,aAAa,cAAc,CAAC,EAAE,EAAE,kBAAkB;QACxD,MAAM,iBAAiB,cAAc,CAAC,EAAE,EAAE,iBAAiB;QAC3D,MAAM,eAAe,cAAc,CAAC,EAAE,EAAE,yBAAyB;QAEjE,uEAAuE;QACvE,MAAM,sBAAsB;QAC5B,MAAM,oBAAoB,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC,iBAAiB,EAAE,IAAI;QAElF,sBAAsB;QACtB,MAAM,WAAW;YACf,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;YACjC,MAAM;gBACJ,SAAS;oBACP,mBAAmB,WAAW,WAAW,OAAO,CAAC;oBACjD,qBAAqB;oBACrB,mBAAmB,WAAW,kBAAkB,OAAO,CAAC;oBACxD,iBAAiB,KAAK,KAAK,CAAC;oBAC5B,eAAe,cAAc,CAAC,EAAE,EAAE,sBAAsB;oBACxD,aAAa,cAAc,CAAC,EAAE,EAAE,WAAW;gBAC7C;gBACA,SAAS;oBACP,qBAAqB,WAAW,CAAC,cAAc,CAAC,EAAE,EAAE,yBAAyB,CAAC,EAAE,OAAO,CAAC;oBACxF,aAAa,WAAW,CAAC,cAAc,CAAC,EAAE,EAAE,mBAAmB,CAAC,EAAE,OAAO,CAAC;oBAC1E,kBAAkB,WAAW,CAAC,cAAc,CAAC,EAAE,EAAE,wBAAwB,CAAC,EAAE,OAAO,CAAC;gBACtF;gBACA,QAAQ;oBACN,aAAa,iBAAiB,GAAG,CAAC,CAAA,MAAO,CAAC;4BACxC,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC,EAAE,OAAO,IAAI,GAAG,EAAE,QAAQ,CAAC,GAAG,MAAM;4BACvD,YAAY,WAAW,CAAC,IAAI,cAAc,IAAI,CAAC,EAAE,OAAO,CAAC;4BACzD,aAAa,KAAK,KAAK,CAAC,IAAI,aAAa,IAAI;4BAC7C,UAAU,SAAS,IAAI,kBAAkB;wBAC3C,CAAC;oBACD,OAAO,gBAAgB,GAAG,CAAC,CAAA,MAAO,CAAC;4BACjC,MAAM,IAAI,IAAI;4BACd,YAAY,WAAW,CAAC,IAAI,cAAc,IAAI,CAAC,EAAE,OAAO,CAAC;4BACzD,QAAQ,SAAS,IAAI,MAAM;4BAC3B,aAAa,KAAK,KAAK,CAAC,IAAI,aAAa,IAAI;wBAC/C,CAAC;gBACH;gBACA,OAAO,iBAAiB,GAAG,CAAC,CAAC,KAAK;oBAChC,4BAA4B;oBAC5B,MAAM,aAAa;wBACjB;wBAAa;wBAAiB;wBAAe;wBAC7C;wBAAc;wBAAiB;wBAAiB;qBACjD;oBAED,MAAM,mBAAmB,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAC9C,CAAC,IAAI,cAAc,IAAI,CAAC,IAAI,MAC5B,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,aAAa,GAAG,EAAE,IAAI,KAAK;oBAGpD,OAAO;wBACL,IAAI,IAAI,OAAO;wBACf,MAAM,UAAU,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,IAAI,OAAO,EAAE;wBACjD,QAAQ,SAAS,IAAI,MAAM;wBAC3B,aAAa,KAAK,KAAK,CAAC,IAAI,aAAa,IAAI;wBAC7C,YAAY,WAAW,CAAC,IAAI,cAAc,IAAI,CAAC,EAAE,OAAO,CAAC;wBACzD,kBAAkB,KAAK,KAAK,CAAC;oBAC/B;gBACF,GAAG,KAAK,CAAC,GAAG;gBACZ,aAAa;oBACX,qBAAqB;oBACrB,iBAAiB;oBACjB,mBAAmB;gBACrB;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}