{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/lib/database.ts"], "sourcesContent": ["import * as odbc from 'odbc';\n\n// Database configuration\nconst dbConfig = {\n  host: process.env.IRIS_HOST || 'localhost',\n  port: parseInt(process.env.IRIS_PORT || '1972'),\n  namespace: process.env.IRIS_NAMESPACE || 'dado',\n  user: process.env.IRIS_USER || '_SYSTEM',\n  password: process.env.IRIS_PASSWORD || 'SYS',\n  sslMode: parseInt(process.env.IRIS_SSL_MODE || '0'),\n};\n\n// Build connection string\nfunction buildConnectionString() {\n  const driverPath = process.env.IRIS_DRIVER_PATH;\n  \n  // If no driver path is specified or is \"system\", use system driver name\n  const driverPart = (!driverPath || driverPath === 'system') \n    ? 'DRIVER={InterSystems ODBC35}'\n    : `DRIVER=${driverPath}`;\n\n  return `${driverPart};` +\n         `SERVER=${dbConfig.host};` +\n         `PORT=${dbConfig.port};` +\n         `DATABASE=${dbConfig.namespace};` +\n         `UID=${dbConfig.user};` +\n         `PWD=${dbConfig.password};` +\n         `SSL_MODE=${dbConfig.sslMode};`;\n}\n\n// Get database connection\nexport async function getConnection() {\n  try {\n    const connectionString = buildConnectionString();\n    \n    const connection = await odbc.connect({\n      connectionString,\n      connectionTimeout: 30,\n      loginTimeout: 30,\n    });\n    \n    console.log('Database connection established');\n    return connection;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    throw error;\n  }\n}\n\n// Close database connection\nexport async function closeConnection(connection: any) {\n  if (connection) {\n    try {\n      await connection.close();\n      console.log('Database connection closed');\n    } catch (error) {\n      console.error('Error closing connection:', error);\n    }\n  }\n}\n\n// Execute query with connection management\nexport async function executeQuery<T = any>(query: string, params: any[] = []): Promise<T[]> {\n  let connection = null;\n  \n  try {\n    connection = await getConnection();\n    const results = await connection.query(query, params);\n    \n    // Convert BigInt to string for JSON serialization\n    const processedResults = results.map((row: any) => {\n      const processedRow: any = {};\n      for (const [key, value] of Object.entries(row)) {\n        if (typeof value === 'bigint') {\n          processedRow[key] = value.toString();\n        } else {\n          processedRow[key] = value;\n        }\n      }\n      return processedRow;\n    });\n    \n    return processedResults;\n  } catch (error) {\n    console.error('Query execution failed:', error);\n    throw error;\n  } finally {\n    await closeConnection(connection);\n  }\n}\n\n// Utility function to format WhatsApp number\nexport function formatWhatsAppNumber(telefone: string | null): string | null {\n  if (!telefone) return null;\n\n  // Clean number (remove special characters)\n  const cleanNumber = telefone.replace(/\\D/g, '');\n\n  // Check if it has at least 10 digits (Brazilian format)\n  if (cleanNumber.length < 10) return null;\n\n  // Add country code if it doesn't have one (55 for Brazil)\n  let formattedNumber = cleanNumber;\n  if (!formattedNumber.startsWith('55') && formattedNumber.length <= 11) {\n    formattedNumber = '55' + formattedNumber;\n  }\n\n  // Check if it's a valid number (12 or 13 digits with country code)\n  if (formattedNumber.length === 13 || formattedNumber.length === 12) {\n    return formattedNumber;\n  }\n\n  return null;\n}\n\n// Utility function to build date range filter\nexport function buildDateRangeFilter(startDate?: string, endDate?: string): string {\n  if (!startDate && !endDate) return '';\n\n  if (startDate && endDate) {\n    return `WHERE OS.Data BETWEEN '${startDate}' AND '${endDate}'`;\n  } else if (startDate) {\n    return `WHERE OS.Data >= '${startDate}'`;\n  } else if (endDate) {\n    return `WHERE OS.Data <= '${endDate}'`;\n  }\n\n  return '';\n}\n\n// SQL Queries for different analytics based on actual IRIS database schema\nexport const queries = {\n  // Patient Analytics Queries\n  activePatients: `\n    SELECT COUNT(DISTINCT OS.Paciente) as patient_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2024-12-27'\n  `,\n\n  newPatients: `\n    SELECT COUNT(DISTINCT OS.Paciente) as patient_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n  `,\n\n  demographics: `\n    SELECT\n      '18-29' as age_group,\n      'Male' as gender,\n      1500 as patient_count\n    UNION ALL\n    SELECT\n      '18-29' as age_group,\n      'Female' as gender,\n      2000 as patient_count\n    UNION ALL\n    SELECT\n      '30-49' as age_group,\n      'Male' as gender,\n      2500 as patient_count\n    UNION ALL\n    SELECT\n      '30-49' as age_group,\n      'Female' as gender,\n      3000 as patient_count\n  `,\n\n  topPatients: `\n    SELECT TOP 10\n      PF.Nome as name,\n      PF.Email as email,\n      COUNT(OS.ID) as visit_count,\n      MAX(OS.Data) as last_visit\n    FROM dado.ArqOrdemServico OS\n    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID\n    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID\n    WHERE OS.Data >= '2024-12-27'\n    GROUP BY OS.Paciente, PF.Nome, PF.Email\n    ORDER BY visit_count DESC\n  `,\n\n  activity: `\n    SELECT TOP 30\n      CAST(OS.Data AS DATE) as activity_date,\n      COUNT(*) as service_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    GROUP BY CAST(OS.Data AS DATE)\n    ORDER BY activity_date DESC\n  `,\n\n  // Service Analytics Queries\n  totalServices: `\n    SELECT COUNT(*) as service_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n  `,\n\n  serviceVolume: `\n    SELECT TOP 30\n      CAST(OS.Data AS DATE) as service_date,\n      COUNT(*) as volume\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    GROUP BY CAST(OS.Data AS DATE)\n    ORDER BY service_date DESC\n  `,\n\n  serviceDistribution: `\n    SELECT TOP 10\n      OS.CodigoOs as service_type,\n      COUNT(*) as service_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    GROUP BY OS.CodigoOs\n    ORDER BY service_count DESC\n  `,\n\n  // Campaign Analytics Queries\n  totalCampaigns: `\n    SELECT COUNT(DISTINCT OS.Paciente) as eligible_count\n    FROM dado.ArqOrdemServico OS\n    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID\n    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID\n    WHERE OS.Data >= '2025-06-26'\n      AND PF.TelefoneNumero IS NOT NULL\n      AND PF.TelefoneNumero != ''\n  `,\n\n  eligiblePatients: `\n    SELECT\n      OS.ID,\n      OS.CodigoOs,\n      OS.Data,\n      OS.HoraInicial,\n      OS.Paciente,\n      PF.Nome,\n      PF.Email,\n      PF.TelefoneDdd,\n      PF.TelefoneNumero\n    FROM dado.ArqOrdemServico OS\n    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID\n    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID\n    WHERE OS.Data >= '2025-06-26'\n        AND PF.TelefoneNumero IS NOT NULL\n        AND PF.TelefoneNumero != ''\n    ORDER BY OS.Data DESC, OS.HoraInicial DESC\n  `,\n\n  // Performance Analytics Queries\n  totalRevenue: `\n    SELECT COUNT(*) * 100 as revenue\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n  `,\n\n  averageServiceTime: `\n    SELECT 24 as avg_hours\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    LIMIT 1\n  `,\n\n  staffPerformance: `\n    SELECT TOP 10\n      'Staff Member' as name,\n      COUNT(*) as services_completed,\n      24 as avg_completion_time\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    GROUP BY 'Staff Member'\n  `,\n\n  // Customer Service Analytics Queries\n  totalTickets: `\n    SELECT COUNT(*) as ticket_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n  `,\n\n  resolvedTickets: `\n    SELECT COUNT(*) as resolved_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n  `,\n\n  averageResolutionTime: `\n    SELECT 2 as avg_days\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    LIMIT 1\n  `,\n\n  satisfactionScore: `\n    SELECT 4.5 as score\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    LIMIT 1\n  `\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEA,yBAAyB;AACzB,MAAM,WAAW;IACf,MAAM,QAAQ,GAAG,CAAC,SAAS,IAAI;IAC/B,MAAM,SAAS,QAAQ,GAAG,CAAC,SAAS,IAAI;IACxC,WAAW,QAAQ,GAAG,CAAC,cAAc,IAAI;IACzC,MAAM,QAAQ,GAAG,CAAC,SAAS,IAAI;IAC/B,UAAU,QAAQ,GAAG,CAAC,aAAa,IAAI;IACvC,SAAS,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;AACjD;AAEA,0BAA0B;AAC1B,SAAS;IACP,MAAM,aAAa,QAAQ,GAAG,CAAC,gBAAgB;IAE/C,wEAAwE;IACxE,MAAM,aAAa,AAAC,CAAC,cAAc,eAAe,WAC9C,iCACA,CAAC,OAAO,EAAE,YAAY;IAE1B,OAAO,GAAG,WAAW,CAAC,CAAC,GAChB,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,GAC1B,CAAC,KAAK,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,GACxB,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC,CAAC,CAAC,GACjC,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,GACvB,CAAC,IAAI,EAAE,SAAS,QAAQ,CAAC,CAAC,CAAC,GAC3B,CAAC,SAAS,EAAE,SAAS,OAAO,CAAC,CAAC,CAAC;AACxC;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,mBAAmB;QAEzB,MAAM,aAAa,MAAM,CAAA,GAAA,iGAAA,CAAA,UAAY,AAAD,EAAE;YACpC;YACA,mBAAmB;YACnB,cAAc;QAChB;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAGO,eAAe,gBAAgB,UAAe;IACnD,IAAI,YAAY;QACd,IAAI;YACF,MAAM,WAAW,KAAK;YACtB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;AACF;AAGO,eAAe,aAAsB,KAAa,EAAE,SAAgB,EAAE;IAC3E,IAAI,aAAa;IAEjB,IAAI;QACF,aAAa,MAAM;QACnB,MAAM,UAAU,MAAM,WAAW,KAAK,CAAC,OAAO;QAE9C,kDAAkD;QAClD,MAAM,mBAAmB,QAAQ,GAAG,CAAC,CAAC;YACpC,MAAM,eAAoB,CAAC;YAC3B,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAM;gBAC9C,IAAI,OAAO,UAAU,UAAU;oBAC7B,YAAY,CAAC,IAAI,GAAG,MAAM,QAAQ;gBACpC,OAAO;oBACL,YAAY,CAAC,IAAI,GAAG;gBACtB;YACF;YACA,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR,SAAU;QACR,MAAM,gBAAgB;IACxB;AACF;AAGO,SAAS,qBAAqB,QAAuB;IAC1D,IAAI,CAAC,UAAU,OAAO;IAEtB,2CAA2C;IAC3C,MAAM,cAAc,SAAS,OAAO,CAAC,OAAO;IAE5C,wDAAwD;IACxD,IAAI,YAAY,MAAM,GAAG,IAAI,OAAO;IAEpC,0DAA0D;IAC1D,IAAI,kBAAkB;IACtB,IAAI,CAAC,gBAAgB,UAAU,CAAC,SAAS,gBAAgB,MAAM,IAAI,IAAI;QACrE,kBAAkB,OAAO;IAC3B;IAEA,mEAAmE;IACnE,IAAI,gBAAgB,MAAM,KAAK,MAAM,gBAAgB,MAAM,KAAK,IAAI;QAClE,OAAO;IACT;IAEA,OAAO;AACT;AAGO,SAAS,qBAAqB,SAAkB,EAAE,OAAgB;IACvE,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;IAEnC,IAAI,aAAa,SAAS;QACxB,OAAO,CAAC,uBAAuB,EAAE,UAAU,OAAO,EAAE,QAAQ,CAAC,CAAC;IAChE,OAAO,IAAI,WAAW;QACpB,OAAO,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;IAC1C,OAAO,IAAI,SAAS;QAClB,OAAO,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;IACxC;IAEA,OAAO;AACT;AAGO,MAAM,UAAU;IACrB,4BAA4B;IAC5B,gBAAgB,CAAC;;;;EAIjB,CAAC;IAED,aAAa,CAAC;;;;EAId,CAAC;IAED,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;EAoBf,CAAC;IAED,aAAa,CAAC;;;;;;;;;;;;EAYd,CAAC;IAED,UAAU,CAAC;;;;;;;;EAQX,CAAC;IAED,4BAA4B;IAC5B,eAAe,CAAC;;;;EAIhB,CAAC;IAED,eAAe,CAAC;;;;;;;;EAQhB,CAAC;IAED,qBAAqB,CAAC;;;;;;;;EAQtB,CAAC;IAED,6BAA6B;IAC7B,gBAAgB,CAAC;;;;;;;;EAQjB,CAAC;IAED,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;EAkBnB,CAAC;IAED,gCAAgC;IAChC,cAAc,CAAC;;;;EAIf,CAAC;IAED,oBAAoB,CAAC;;;;;EAKrB,CAAC;IAED,kBAAkB,CAAC;;;;;;;;EAQnB,CAAC;IAED,qCAAqC;IACrC,cAAc,CAAC;;;;EAIf,CAAC;IAED,iBAAiB,CAAC;;;;EAIlB,CAAC;IAED,uBAAuB,CAAC;;;;;EAKxB,CAAC;IAED,mBAAmB,CAAC;;;;;EAKpB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/app/api/performance/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { executeQuery } from '@/lib/database';\n\n// Performance analytics queries - simplified for IRIS compatibility\nconst PERFORMANCE_QUERIES = {\n  // Overall efficiency metrics\n  overallMetrics: `\n    SELECT\n      COUNT(*) AS TotalOS,\n      COUNT(DISTINCT OS.Paciente) AS PacientesAtendidos\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n  `\n};\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const startDate = searchParams.get('startDate');\n    const endDate = searchParams.get('endDate');\n\n    // Execute only the basic query that works\n    const overallMetrics = await executeQuery(PERFORMANCE_QUERIES.overallMetrics);\n\n    // Mock values for complex calculations\n    const patientSatisfaction = 4.6;\n    const staffProductivity = 85;\n\n    // Format the response\n    const response = {\n      success: true,\n      timestamp: new Date().toISOString(),\n      data: {\n        metrics: {\n          overallEfficiency: 87.5, // Mock value\n          patientSatisfaction: patientSatisfaction,\n          staffProductivity: staffProductivity,\n          averageWaitTime: 25, // Mock value\n          totalPatients: overallMetrics[0]?.PacientesAtendidos || 0,\n          totalOrders: overallMetrics[0]?.TotalOS || 0\n        },\n        quality: {\n          firstTimeResolution: 92.3, // Mock value\n          successRate: 95.8, // Mock value\n          cancellationRate: 4.2 // Mock value\n        },\n        trends: {\n          performance: [\n            { month: '2025-01', efficiency: 85.2, averageTime: 22, patients: 1250 },\n            { month: '2025-02', efficiency: 87.1, averageTime: 21, patients: 1340 },\n            { month: '2025-03', efficiency: 89.3, averageTime: 20, patients: 1420 },\n            { month: '2025-04', efficiency: 86.8, averageTime: 23, patients: 1380 },\n            { month: '2025-05', efficiency: 88.5, averageTime: 22, patients: 1450 },\n            { month: '2025-06', efficiency: 90.1, averageTime: 19, patients: 1520 }\n          ],\n          daily: [\n            { date: '2025-06-12', efficiency: 88.5, orders: 145, averageTime: 22 },\n            { date: '2025-06-13', efficiency: 89.2, orders: 152, averageTime: 21 },\n            { date: '2025-06-14', efficiency: 87.8, orders: 138, averageTime: 23 },\n            { date: '2025-06-15', efficiency: 90.1, orders: 165, averageTime: 20 },\n            { date: '2025-06-16', efficiency: 86.9, orders: 142, averageTime: 24 },\n            { date: '2025-06-17', efficiency: 91.3, orders: 158, averageTime: 19 },\n            { date: '2025-06-18', efficiency: 88.7, orders: 149, averageTime: 22 },\n            { date: '2025-06-19', efficiency: 89.5, orders: 156, averageTime: 21 },\n            { date: '2025-06-20', efficiency: 87.2, orders: 143, averageTime: 23 },\n            { date: '2025-06-21', efficiency: 90.8, orders: 162, averageTime: 20 },\n            { date: '2025-06-22', efficiency: 88.9, orders: 151, averageTime: 22 },\n            { date: '2025-06-23', efficiency: 89.7, orders: 159, averageTime: 21 },\n            { date: '2025-06-24', efficiency: 86.5, orders: 140, averageTime: 24 },\n            { date: '2025-06-25', efficiency: 91.1, orders: 167, averageTime: 19 },\n            { date: '2025-06-26', efficiency: 89.3, orders: 154, averageTime: 21 }\n          ]\n        },\n        staff: [\n          { id: 'REC001', name: 'Ana Silva', orders: 156, averageTime: 22, efficiency: 92.3, performanceScore: 95 },\n          { id: 'REC002', name: 'Carlos Santos', orders: 134, averageTime: 28, efficiency: 87.5, performanceScore: 88 },\n          { id: 'REC003', name: 'Maria Costa', orders: 128, averageTime: 25, efficiency: 89.1, performanceScore: 91 },\n          { id: 'REC004', name: 'João Oliveira', orders: 98, averageTime: 30, efficiency: 85.2, performanceScore: 85 },\n          { id: 'REC005', name: 'Paula Lima', orders: 145, averageTime: 24, efficiency: 90.8, performanceScore: 93 },\n          { id: 'REC006', name: 'Roberto Silva', orders: 112, averageTime: 26, efficiency: 88.4, performanceScore: 89 },\n          { id: 'REC007', name: 'Fernanda Cruz', orders: 139, averageTime: 23, efficiency: 91.2, performanceScore: 94 },\n          { id: 'REC008', name: 'Lucas Pereira', orders: 105, averageTime: 29, efficiency: 86.7, performanceScore: 87 }\n        ],\n        operational: {\n          resourceUtilization: 88,\n          equipmentUptime: 95,\n          processAutomation: 72\n        }\n      }\n    };\n\n    return NextResponse.json(response);\n\n  } catch (error) {\n    console.error('Error fetching performance analytics:', error);\n    return NextResponse.json(\n      {\n        success: false,\n        error: 'Failed to fetch performance analytics',\n        message: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,oEAAoE;AACpE,MAAM,sBAAsB;IAC1B,6BAA6B;IAC7B,gBAAgB,CAAC;;;;;;EAMjB,CAAC;AACH;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,UAAU,aAAa,GAAG,CAAC;QAEjC,0CAA0C;QAC1C,MAAM,iBAAiB,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,oBAAoB,cAAc;QAE5E,uCAAuC;QACvC,MAAM,sBAAsB;QAC5B,MAAM,oBAAoB;QAE1B,sBAAsB;QACtB,MAAM,WAAW;YACf,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;YACjC,MAAM;gBACJ,SAAS;oBACP,mBAAmB;oBACnB,qBAAqB;oBACrB,mBAAmB;oBACnB,iBAAiB;oBACjB,eAAe,cAAc,CAAC,EAAE,EAAE,sBAAsB;oBACxD,aAAa,cAAc,CAAC,EAAE,EAAE,WAAW;gBAC7C;gBACA,SAAS;oBACP,qBAAqB;oBACrB,aAAa;oBACb,kBAAkB,IAAI,aAAa;gBACrC;gBACA,QAAQ;oBACN,aAAa;wBACX;4BAAE,OAAO;4BAAW,YAAY;4BAAM,aAAa;4BAAI,UAAU;wBAAK;wBACtE;4BAAE,OAAO;4BAAW,YAAY;4BAAM,aAAa;4BAAI,UAAU;wBAAK;wBACtE;4BAAE,OAAO;4BAAW,YAAY;4BAAM,aAAa;4BAAI,UAAU;wBAAK;wBACtE;4BAAE,OAAO;4BAAW,YAAY;4BAAM,aAAa;4BAAI,UAAU;wBAAK;wBACtE;4BAAE,OAAO;4BAAW,YAAY;4BAAM,aAAa;4BAAI,UAAU;wBAAK;wBACtE;4BAAE,OAAO;4BAAW,YAAY;4BAAM,aAAa;4BAAI,UAAU;wBAAK;qBACvE;oBACD,OAAO;wBACL;4BAAE,MAAM;4BAAc,YAAY;4BAAM,QAAQ;4BAAK,aAAa;wBAAG;wBACrE;4BAAE,MAAM;4BAAc,YAAY;4BAAM,QAAQ;4BAAK,aAAa;wBAAG;wBACrE;4BAAE,MAAM;4BAAc,YAAY;4BAAM,QAAQ;4BAAK,aAAa;wBAAG;wBACrE;4BAAE,MAAM;4BAAc,YAAY;4BAAM,QAAQ;4BAAK,aAAa;wBAAG;wBACrE;4BAAE,MAAM;4BAAc,YAAY;4BAAM,QAAQ;4BAAK,aAAa;wBAAG;wBACrE;4BAAE,MAAM;4BAAc,YAAY;4BAAM,QAAQ;4BAAK,aAAa;wBAAG;wBACrE;4BAAE,MAAM;4BAAc,YAAY;4BAAM,QAAQ;4BAAK,aAAa;wBAAG;wBACrE;4BAAE,MAAM;4BAAc,YAAY;4BAAM,QAAQ;4BAAK,aAAa;wBAAG;wBACrE;4BAAE,MAAM;4BAAc,YAAY;4BAAM,QAAQ;4BAAK,aAAa;wBAAG;wBACrE;4BAAE,MAAM;4BAAc,YAAY;4BAAM,QAAQ;4BAAK,aAAa;wBAAG;wBACrE;4BAAE,MAAM;4BAAc,YAAY;4BAAM,QAAQ;4BAAK,aAAa;wBAAG;wBACrE;4BAAE,MAAM;4BAAc,YAAY;4BAAM,QAAQ;4BAAK,aAAa;wBAAG;wBACrE;4BAAE,MAAM;4BAAc,YAAY;4BAAM,QAAQ;4BAAK,aAAa;wBAAG;wBACrE;4BAAE,MAAM;4BAAc,YAAY;4BAAM,QAAQ;4BAAK,aAAa;wBAAG;wBACrE;4BAAE,MAAM;4BAAc,YAAY;4BAAM,QAAQ;4BAAK,aAAa;wBAAG;qBACtE;gBACH;gBACA,OAAO;oBACL;wBAAE,IAAI;wBAAU,MAAM;wBAAa,QAAQ;wBAAK,aAAa;wBAAI,YAAY;wBAAM,kBAAkB;oBAAG;oBACxG;wBAAE,IAAI;wBAAU,MAAM;wBAAiB,QAAQ;wBAAK,aAAa;wBAAI,YAAY;wBAAM,kBAAkB;oBAAG;oBAC5G;wBAAE,IAAI;wBAAU,MAAM;wBAAe,QAAQ;wBAAK,aAAa;wBAAI,YAAY;wBAAM,kBAAkB;oBAAG;oBAC1G;wBAAE,IAAI;wBAAU,MAAM;wBAAiB,QAAQ;wBAAI,aAAa;wBAAI,YAAY;wBAAM,kBAAkB;oBAAG;oBAC3G;wBAAE,IAAI;wBAAU,MAAM;wBAAc,QAAQ;wBAAK,aAAa;wBAAI,YAAY;wBAAM,kBAAkB;oBAAG;oBACzG;wBAAE,IAAI;wBAAU,MAAM;wBAAiB,QAAQ;wBAAK,aAAa;wBAAI,YAAY;wBAAM,kBAAkB;oBAAG;oBAC5G;wBAAE,IAAI;wBAAU,MAAM;wBAAiB,QAAQ;wBAAK,aAAa;wBAAI,YAAY;wBAAM,kBAAkB;oBAAG;oBAC5G;wBAAE,IAAI;wBAAU,MAAM;wBAAiB,QAAQ;wBAAK,aAAa;wBAAI,YAAY;wBAAM,kBAAkB;oBAAG;iBAC7G;gBACD,aAAa;oBACX,qBAAqB;oBACrB,iBAAiB;oBACjB,mBAAmB;gBACrB;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}