{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/lib/database.ts"], "sourcesContent": ["import * as odbc from 'odbc';\n\n// Database configuration\nconst dbConfig = {\n  host: process.env.IRIS_HOST || 'localhost',\n  port: parseInt(process.env.IRIS_PORT || '1972'),\n  namespace: process.env.IRIS_NAMESPACE || 'dado',\n  user: process.env.IRIS_USER || '_SYSTEM',\n  password: process.env.IRIS_PASSWORD || 'SYS',\n  sslMode: parseInt(process.env.IRIS_SSL_MODE || '0'),\n};\n\n// Build connection string\nfunction buildConnectionString() {\n  const driverPath = process.env.IRIS_DRIVER_PATH;\n  \n  // If no driver path is specified or is \"system\", use system driver name\n  const driverPart = (!driverPath || driverPath === 'system') \n    ? 'DRIVER={InterSystems ODBC35}'\n    : `DRIVER=${driverPath}`;\n\n  return `${driverPart};` +\n         `SERVER=${dbConfig.host};` +\n         `PORT=${dbConfig.port};` +\n         `DATABASE=${dbConfig.namespace};` +\n         `UID=${dbConfig.user};` +\n         `PWD=${dbConfig.password};` +\n         `SSL_MODE=${dbConfig.sslMode};`;\n}\n\n// Get database connection\nexport async function getConnection() {\n  try {\n    const connectionString = buildConnectionString();\n    \n    const connection = await odbc.connect({\n      connectionString,\n      connectionTimeout: 30,\n      loginTimeout: 30,\n    });\n    \n    console.log('Database connection established');\n    return connection;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    throw error;\n  }\n}\n\n// Close database connection\nexport async function closeConnection(connection: any) {\n  if (connection) {\n    try {\n      await connection.close();\n      console.log('Database connection closed');\n    } catch (error) {\n      console.error('Error closing connection:', error);\n    }\n  }\n}\n\n// Execute query with connection management\nexport async function executeQuery<T = any>(query: string, params: any[] = []): Promise<T[]> {\n  let connection = null;\n  \n  try {\n    connection = await getConnection();\n    const results = await connection.query(query, params);\n    \n    // Convert BigInt to string for JSON serialization\n    const processedResults = results.map((row: any) => {\n      const processedRow: any = {};\n      for (const [key, value] of Object.entries(row)) {\n        if (typeof value === 'bigint') {\n          processedRow[key] = value.toString();\n        } else {\n          processedRow[key] = value;\n        }\n      }\n      return processedRow;\n    });\n    \n    return processedResults;\n  } catch (error) {\n    console.error('Query execution failed:', error);\n    throw error;\n  } finally {\n    await closeConnection(connection);\n  }\n}\n\n// Utility function to format WhatsApp number\nexport function formatWhatsAppNumber(telefone: string | null): string | null {\n  if (!telefone) return null;\n\n  // Clean number (remove special characters)\n  const cleanNumber = telefone.replace(/\\D/g, '');\n\n  // Check if it has at least 10 digits (Brazilian format)\n  if (cleanNumber.length < 10) return null;\n\n  // Add country code if it doesn't have one (55 for Brazil)\n  let formattedNumber = cleanNumber;\n  if (!formattedNumber.startsWith('55') && formattedNumber.length <= 11) {\n    formattedNumber = '55' + formattedNumber;\n  }\n\n  // Check if it's a valid number (12 or 13 digits with country code)\n  if (formattedNumber.length === 13 || formattedNumber.length === 12) {\n    return formattedNumber;\n  }\n\n  return null;\n}\n\n// Utility function to build date range filter\nexport function buildDateRangeFilter(startDate?: string, endDate?: string): string {\n  if (!startDate && !endDate) return '';\n  \n  if (startDate && endDate) {\n    return `WHERE OS.Data BETWEEN '${startDate}' AND '${endDate}'`;\n  } else if (startDate) {\n    return `WHERE OS.Data >= '${startDate}'`;\n  } else if (endDate) {\n    return `WHERE OS.Data <= '${endDate}'`;\n  }\n  \n  return '';\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA,yBAAyB;AACzB,MAAM,WAAW;IACf,MAAM,QAAQ,GAAG,CAAC,SAAS,IAAI;IAC/B,MAAM,SAAS,QAAQ,GAAG,CAAC,SAAS,IAAI;IACxC,WAAW,QAAQ,GAAG,CAAC,cAAc,IAAI;IACzC,MAAM,QAAQ,GAAG,CAAC,SAAS,IAAI;IAC/B,UAAU,QAAQ,GAAG,CAAC,aAAa,IAAI;IACvC,SAAS,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;AACjD;AAEA,0BAA0B;AAC1B,SAAS;IACP,MAAM,aAAa,QAAQ,GAAG,CAAC,gBAAgB;IAE/C,wEAAwE;IACxE,MAAM,aAAa,AAAC,CAAC,cAAc,eAAe,WAC9C,iCACA,CAAC,OAAO,EAAE,YAAY;IAE1B,OAAO,GAAG,WAAW,CAAC,CAAC,GAChB,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,GAC1B,CAAC,KAAK,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,GACxB,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC,CAAC,CAAC,GACjC,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,GACvB,CAAC,IAAI,EAAE,SAAS,QAAQ,CAAC,CAAC,CAAC,GAC3B,CAAC,SAAS,EAAE,SAAS,OAAO,CAAC,CAAC,CAAC;AACxC;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,mBAAmB;QAEzB,MAAM,aAAa,MAAM,CAAA,GAAA,iGAAA,CAAA,UAAY,AAAD,EAAE;YACpC;YACA,mBAAmB;YACnB,cAAc;QAChB;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAGO,eAAe,gBAAgB,UAAe;IACnD,IAAI,YAAY;QACd,IAAI;YACF,MAAM,WAAW,KAAK;YACtB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;AACF;AAGO,eAAe,aAAsB,KAAa,EAAE,SAAgB,EAAE;IAC3E,IAAI,aAAa;IAEjB,IAAI;QACF,aAAa,MAAM;QACnB,MAAM,UAAU,MAAM,WAAW,KAAK,CAAC,OAAO;QAE9C,kDAAkD;QAClD,MAAM,mBAAmB,QAAQ,GAAG,CAAC,CAAC;YACpC,MAAM,eAAoB,CAAC;YAC3B,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAM;gBAC9C,IAAI,OAAO,UAAU,UAAU;oBAC7B,YAAY,CAAC,IAAI,GAAG,MAAM,QAAQ;gBACpC,OAAO;oBACL,YAAY,CAAC,IAAI,GAAG;gBACtB;YACF;YACA,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR,SAAU;QACR,MAAM,gBAAgB;IACxB;AACF;AAGO,SAAS,qBAAqB,QAAuB;IAC1D,IAAI,CAAC,UAAU,OAAO;IAEtB,2CAA2C;IAC3C,MAAM,cAAc,SAAS,OAAO,CAAC,OAAO;IAE5C,wDAAwD;IACxD,IAAI,YAAY,MAAM,GAAG,IAAI,OAAO;IAEpC,0DAA0D;IAC1D,IAAI,kBAAkB;IACtB,IAAI,CAAC,gBAAgB,UAAU,CAAC,SAAS,gBAAgB,MAAM,IAAI,IAAI;QACrE,kBAAkB,OAAO;IAC3B;IAEA,mEAAmE;IACnE,IAAI,gBAAgB,MAAM,KAAK,MAAM,gBAAgB,MAAM,KAAK,IAAI;QAClE,OAAO;IACT;IAEA,OAAO;AACT;AAGO,SAAS,qBAAqB,SAAkB,EAAE,OAAgB;IACvE,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;IAEnC,IAAI,aAAa,SAAS;QACxB,OAAO,CAAC,uBAAuB,EAAE,UAAU,OAAO,EAAE,QAAQ,CAAC,CAAC;IAChE,OAAO,IAAI,WAAW;QACpB,OAAO,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;IAC1C,OAAO,IAAI,SAAS;QAClB,OAAO,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;IACxC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/app/api/customer-service/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { executeQuery } from '@/lib/database';\n\n// Customer service analytics queries\nconst CUSTOMER_SERVICE_QUERIES = {\n  // Support ticket metrics (using OS data as proxy)\n  ticketMetrics: `\n    SELECT\n      COUNT(*) AS TotalTickets,\n      COUNT(CASE WHEN OS.OsStatus = 1 THEN 1 END) AS ResolvedTickets,\n      COUNT(CASE WHEN OS.OsStatus = 0 THEN 1 END) AS CancelledTickets,\n      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS AvgResolutionTimeMin\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)\n      AND OS.HoraInicial IS NOT NULL \n      AND OS.HoraFinal IS NOT NULL\n  `,\n\n  // Response time distribution\n  responseTimeDistribution: `\n    SELECT\n      CASE\n        WHEN DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal) <= 15 THEN '0-15 min'\n        WHEN DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal) <= 30 THEN '16-30 min'\n        WHEN DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal) <= 60 THEN '31-60 min'\n        ELSE '60+ min'\n      END AS TimeRange,\n      COUNT(*) AS Count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)\n      AND OS.HoraInicial IS NOT NULL \n      AND OS.HoraFinal IS NOT NULL\n    GROUP BY 1\n    ORDER BY \n      CASE TimeRange\n        WHEN '0-15 min' THEN 1\n        WHEN '16-30 min' THEN 2\n        WHEN '31-60 min' THEN 3\n        ELSE 4\n      END\n  `,\n\n  // Daily ticket volume\n  dailyTicketVolume: `\n    SELECT\n      OS.Data,\n      COUNT(*) AS TicketCount,\n      COUNT(CASE WHEN OS.OsStatus = 1 THEN 1 END) AS ResolvedCount,\n      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS AvgTimeMin\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)\n      AND OS.HoraInicial IS NOT NULL \n      AND OS.HoraFinal IS NOT NULL\n    GROUP BY OS.Data\n    ORDER BY OS.Data\n  `,\n\n  // Channel distribution (online vs presential)\n  channelDistribution: `\n    SELECT\n      CASE \n        WHEN OS.DisponivelWeb = 1 THEN 'Online'\n        ELSE 'Presencial'\n      END AS Channel,\n      COUNT(*) AS Count,\n      COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() AS Percentage\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)\n    GROUP BY OS.DisponivelWeb\n  `,\n\n  // Agent performance\n  agentPerformance: `\n    SELECT\n      OS.Recepcionista AS AgentId,\n      COUNT(*) AS TicketsHandled,\n      COUNT(CASE WHEN OS.OsStatus = 1 THEN 1 END) AS TicketsResolved,\n      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS AvgResolutionTimeMin,\n      COUNT(CASE WHEN OS.OsStatus = 1 THEN 1 END) * 100.0 / COUNT(*) AS ResolutionRate\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)\n      AND OS.Recepcionista IS NOT NULL\n      AND OS.HoraInicial IS NOT NULL \n      AND OS.HoraFinal IS NOT NULL\n    GROUP BY OS.Recepcionista\n    ORDER BY TicketsHandled DESC\n    FETCH FIRST 10 ROWS ONLY\n  `,\n\n  // Service type distribution\n  serviceTypeDistribution: `\n    SELECT\n      COALESCE(OS.TipoAtendimento, 'Não especificado') AS ServiceType,\n      COUNT(*) AS Count,\n      COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() AS Percentage\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)\n    GROUP BY OS.TipoAtendimento\n    ORDER BY Count DESC\n  `\n};\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const startDate = searchParams.get('startDate');\n    const endDate = searchParams.get('endDate');\n\n    // Execute all customer service analytics queries\n    const [\n      ticketMetrics,\n      responseTimeDistribution,\n      dailyTicketVolume,\n      channelDistribution,\n      agentPerformance,\n      serviceTypeDistribution\n    ] = await Promise.all([\n      executeQuery(CUSTOMER_SERVICE_QUERIES.ticketMetrics),\n      executeQuery(CUSTOMER_SERVICE_QUERIES.responseTimeDistribution),\n      executeQuery(CUSTOMER_SERVICE_QUERIES.dailyTicketVolume),\n      executeQuery(CUSTOMER_SERVICE_QUERIES.channelDistribution),\n      executeQuery(CUSTOMER_SERVICE_QUERIES.agentPerformance),\n      executeQuery(CUSTOMER_SERVICE_QUERIES.serviceTypeDistribution)\n    ]);\n\n    // Calculate derived metrics\n    const totalTickets = ticketMetrics[0]?.TotalTickets || 0;\n    const resolvedTickets = ticketMetrics[0]?.ResolvedTickets || 0;\n    const avgResolutionTime = Math.round(ticketMetrics[0]?.AvgResolutionTimeMin || 0);\n    const resolutionRate = totalTickets > 0 ? (resolvedTickets / totalTickets) * 100 : 0;\n\n    // Mock satisfaction data (would come from surveys)\n    const satisfactionScore = 4.3;\n    const satisfactionTrend = [4.1, 4.2, 4.3, 4.4, 4.3, 4.2, 4.3];\n\n    // Format the response\n    const response = {\n      success: true,\n      timestamp: new Date().toISOString(),\n      data: {\n        metrics: {\n          totalTickets,\n          resolvedTickets,\n          avgResolutionTime,\n          resolutionRate: parseFloat(resolutionRate.toFixed(1)),\n          satisfactionScore,\n          firstContactResolution: 78.5 // Mock data\n        },\n        trends: {\n          volume: dailyTicketVolume.map(row => ({\n            date: row.Data,\n            total: parseInt(row.TicketCount),\n            resolved: parseInt(row.ResolvedCount),\n            avgTime: Math.round(row.AvgTimeMin || 0)\n          })),\n          satisfaction: satisfactionTrend.map((score, index) => ({\n            week: `Week ${index + 1}`,\n            score\n          }))\n        },\n        distribution: {\n          responseTime: responseTimeDistribution.map(row => ({\n            range: row.TimeRange,\n            count: parseInt(row.Count)\n          })),\n          channels: channelDistribution.map(row => ({\n            channel: row.Channel,\n            count: parseInt(row.Count),\n            percentage: parseFloat(row.Percentage.toFixed(1))\n          })),\n          serviceTypes: serviceTypeDistribution.map(row => ({\n            type: row.ServiceType,\n            count: parseInt(row.Count),\n            percentage: parseFloat(row.Percentage.toFixed(1))\n          }))\n        },\n        agents: agentPerformance.map((row, index) => {\n          // Mock agent names for demo\n          const agentNames = [\n            'Ana Silva', 'Carlos Santos', 'Maria Costa', 'João Oliveira',\n            'Paula Lima', 'Roberto Silva', 'Fernanda Cruz', 'Lucas Pereira',\n            'Juliana Alves', 'Pedro Rocha'\n          ];\n\n          return {\n            id: row.AgentId,\n            name: agentNames[index] || `Agent ${row.AgentId}`,\n            ticketsHandled: parseInt(row.TicketsHandled),\n            ticketsResolved: parseInt(row.TicketsResolved),\n            avgResolutionTime: Math.round(row.AvgResolutionTimeMin || 0),\n            resolutionRate: parseFloat((row.ResolutionRate || 0).toFixed(1)),\n            satisfactionScore: 4.0 + Math.random() * 1.0 // Mock satisfaction\n          };\n        }),\n        recentTickets: [\n          // Mock recent tickets for demo\n          {\n            id: 'T001',\n            subject: 'Problema com agendamento online',\n            status: 'open',\n            priority: 'high',\n            agent: 'Ana Silva',\n            customer: 'João Santos',\n            createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n            channel: 'email'\n          },\n          {\n            id: 'T002',\n            subject: 'Dúvida sobre resultado de exame',\n            status: 'resolved',\n            priority: 'medium',\n            agent: 'Carlos Santos',\n            customer: 'Maria Oliveira',\n            createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n            channel: 'phone'\n          },\n          {\n            id: 'T003',\n            subject: 'Solicitação de segunda via',\n            status: 'in_progress',\n            priority: 'low',\n            agent: 'Maria Costa',\n            customer: 'Pedro Silva',\n            createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n            channel: 'whatsapp'\n          }\n        ]\n      }\n    };\n\n    return NextResponse.json(response);\n\n  } catch (error) {\n    console.error('Error fetching customer service analytics:', error);\n    return NextResponse.json(\n      {\n        success: false,\n        error: 'Failed to fetch customer service analytics',\n        message: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,qCAAqC;AACrC,MAAM,2BAA2B;IAC/B,kDAAkD;IAClD,eAAe,CAAC;;;;;;;;;;EAUhB,CAAC;IAED,6BAA6B;IAC7B,0BAA0B,CAAC;;;;;;;;;;;;;;;;;;;;;EAqB3B,CAAC;IAED,sBAAsB;IACtB,mBAAmB,CAAC;;;;;;;;;;;;EAYpB,CAAC;IAED,8CAA8C;IAC9C,qBAAqB,CAAC;;;;;;;;;;;EAWtB,CAAC;IAED,oBAAoB;IACpB,kBAAkB,CAAC;;;;;;;;;;;;;;;EAenB,CAAC;IAED,4BAA4B;IAC5B,yBAAyB,CAAC;;;;;;;;;EAS1B,CAAC;AACH;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,UAAU,aAAa,GAAG,CAAC;QAEjC,iDAAiD;QACjD,MAAM,CACJ,eACA,0BACA,mBACA,qBACA,kBACA,wBACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,yBAAyB,aAAa;YACnD,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,yBAAyB,wBAAwB;YAC9D,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,yBAAyB,iBAAiB;YACvD,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,yBAAyB,mBAAmB;YACzD,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,yBAAyB,gBAAgB;YACtD,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,yBAAyB,uBAAuB;SAC9D;QAED,4BAA4B;QAC5B,MAAM,eAAe,aAAa,CAAC,EAAE,EAAE,gBAAgB;QACvD,MAAM,kBAAkB,aAAa,CAAC,EAAE,EAAE,mBAAmB;QAC7D,MAAM,oBAAoB,KAAK,KAAK,CAAC,aAAa,CAAC,EAAE,EAAE,wBAAwB;QAC/E,MAAM,iBAAiB,eAAe,IAAI,AAAC,kBAAkB,eAAgB,MAAM;QAEnF,mDAAmD;QACnD,MAAM,oBAAoB;QAC1B,MAAM,oBAAoB;YAAC;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;QAE7D,sBAAsB;QACtB,MAAM,WAAW;YACf,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;YACjC,MAAM;gBACJ,SAAS;oBACP;oBACA;oBACA;oBACA,gBAAgB,WAAW,eAAe,OAAO,CAAC;oBAClD;oBACA,wBAAwB,KAAK,YAAY;gBAC3C;gBACA,QAAQ;oBACN,QAAQ,kBAAkB,GAAG,CAAC,CAAA,MAAO,CAAC;4BACpC,MAAM,IAAI,IAAI;4BACd,OAAO,SAAS,IAAI,WAAW;4BAC/B,UAAU,SAAS,IAAI,aAAa;4BACpC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,IAAI;wBACxC,CAAC;oBACD,cAAc,kBAAkB,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;4BACrD,MAAM,CAAC,KAAK,EAAE,QAAQ,GAAG;4BACzB;wBACF,CAAC;gBACH;gBACA,cAAc;oBACZ,cAAc,yBAAyB,GAAG,CAAC,CAAA,MAAO,CAAC;4BACjD,OAAO,IAAI,SAAS;4BACpB,OAAO,SAAS,IAAI,KAAK;wBAC3B,CAAC;oBACD,UAAU,oBAAoB,GAAG,CAAC,CAAA,MAAO,CAAC;4BACxC,SAAS,IAAI,OAAO;4BACpB,OAAO,SAAS,IAAI,KAAK;4BACzB,YAAY,WAAW,IAAI,UAAU,CAAC,OAAO,CAAC;wBAChD,CAAC;oBACD,cAAc,wBAAwB,GAAG,CAAC,CAAA,MAAO,CAAC;4BAChD,MAAM,IAAI,WAAW;4BACrB,OAAO,SAAS,IAAI,KAAK;4BACzB,YAAY,WAAW,IAAI,UAAU,CAAC,OAAO,CAAC;wBAChD,CAAC;gBACH;gBACA,QAAQ,iBAAiB,GAAG,CAAC,CAAC,KAAK;oBACjC,4BAA4B;oBAC5B,MAAM,aAAa;wBACjB;wBAAa;wBAAiB;wBAAe;wBAC7C;wBAAc;wBAAiB;wBAAiB;wBAChD;wBAAiB;qBAClB;oBAED,OAAO;wBACL,IAAI,IAAI,OAAO;wBACf,MAAM,UAAU,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,IAAI,OAAO,EAAE;wBACjD,gBAAgB,SAAS,IAAI,cAAc;wBAC3C,iBAAiB,SAAS,IAAI,eAAe;wBAC7C,mBAAmB,KAAK,KAAK,CAAC,IAAI,oBAAoB,IAAI;wBAC1D,gBAAgB,WAAW,CAAC,IAAI,cAAc,IAAI,CAAC,EAAE,OAAO,CAAC;wBAC7D,mBAAmB,MAAM,KAAK,MAAM,KAAK,IAAI,oBAAoB;oBACnE;gBACF;gBACA,eAAe;oBACb,+BAA+B;oBAC/B;wBACE,IAAI;wBACJ,SAAS;wBACT,QAAQ;wBACR,UAAU;wBACV,OAAO;wBACP,UAAU;wBACV,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;wBAChE,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,SAAS;wBACT,QAAQ;wBACR,UAAU;wBACV,OAAO;wBACP,UAAU;wBACV,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;wBAChE,SAAS;oBACX;oBACA;wBACE,IAAI;wBACJ,SAAS;wBACT,QAAQ;wBACR,UAAU;wBACV,OAAO;wBACP,UAAU;wBACV,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;wBAChE,SAAS;oBACX;iBACD;YACH;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}