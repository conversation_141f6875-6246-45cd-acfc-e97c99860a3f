module.exports = {

"[project]/.next-internal/server/app/api/services/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/odbc [external] (odbc, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("odbc", () => require("odbc"));

module.exports = mod;
}}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "buildDateRangeFilter": (()=>buildDateRangeFilter),
    "closeConnection": (()=>closeConnection),
    "executeQuery": (()=>executeQuery),
    "formatWhatsAppNumber": (()=>formatWhatsAppNumber),
    "getConnection": (()=>getConnection)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$odbc__$5b$external$5d$__$28$odbc$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/odbc [external] (odbc, cjs)");
;
// Database configuration
const dbConfig = {
    host: process.env.IRIS_HOST || 'localhost',
    port: parseInt(process.env.IRIS_PORT || '1972'),
    namespace: process.env.IRIS_NAMESPACE || 'dado',
    user: process.env.IRIS_USER || '_SYSTEM',
    password: process.env.IRIS_PASSWORD || 'SYS',
    sslMode: parseInt(process.env.IRIS_SSL_MODE || '0')
};
// Build connection string
function buildConnectionString() {
    const driverPath = process.env.IRIS_DRIVER_PATH;
    // If no driver path is specified or is "system", use system driver name
    const driverPart = !driverPath || driverPath === 'system' ? 'DRIVER={InterSystems ODBC35}' : `DRIVER=${driverPath}`;
    return `${driverPart};` + `SERVER=${dbConfig.host};` + `PORT=${dbConfig.port};` + `DATABASE=${dbConfig.namespace};` + `UID=${dbConfig.user};` + `PWD=${dbConfig.password};` + `SSL_MODE=${dbConfig.sslMode};`;
}
async function getConnection() {
    try {
        const connectionString = buildConnectionString();
        const connection = await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$odbc__$5b$external$5d$__$28$odbc$2c$__cjs$29$__["connect"])({
            connectionString,
            connectionTimeout: 30,
            loginTimeout: 30
        });
        console.log('Database connection established');
        return connection;
    } catch (error) {
        console.error('Database connection failed:', error);
        throw error;
    }
}
async function closeConnection(connection) {
    if (connection) {
        try {
            await connection.close();
            console.log('Database connection closed');
        } catch (error) {
            console.error('Error closing connection:', error);
        }
    }
}
async function executeQuery(query, params = []) {
    let connection = null;
    try {
        connection = await getConnection();
        const results = await connection.query(query, params);
        // Convert BigInt to string for JSON serialization
        const processedResults = results.map((row)=>{
            const processedRow = {};
            for (const [key, value] of Object.entries(row)){
                if (typeof value === 'bigint') {
                    processedRow[key] = value.toString();
                } else {
                    processedRow[key] = value;
                }
            }
            return processedRow;
        });
        return processedResults;
    } catch (error) {
        console.error('Query execution failed:', error);
        throw error;
    } finally{
        await closeConnection(connection);
    }
}
function formatWhatsAppNumber(telefone) {
    if (!telefone) return null;
    // Clean number (remove special characters)
    const cleanNumber = telefone.replace(/\D/g, '');
    // Check if it has at least 10 digits (Brazilian format)
    if (cleanNumber.length < 10) return null;
    // Add country code if it doesn't have one (55 for Brazil)
    let formattedNumber = cleanNumber;
    if (!formattedNumber.startsWith('55') && formattedNumber.length <= 11) {
        formattedNumber = '55' + formattedNumber;
    }
    // Check if it's a valid number (12 or 13 digits with country code)
    if (formattedNumber.length === 13 || formattedNumber.length === 12) {
        return formattedNumber;
    }
    return null;
}
function buildDateRangeFilter(startDate, endDate) {
    if (!startDate && !endDate) return '';
    if (startDate && endDate) {
        return `WHERE OS.Data BETWEEN '${startDate}' AND '${endDate}'`;
    } else if (startDate) {
        return `WHERE OS.Data >= '${startDate}'`;
    } else if (endDate) {
        return `WHERE OS.Data <= '${endDate}'`;
    }
    return '';
}
}}),
"[project]/src/app/api/services/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
// Service analytics queries
const SERVICE_QUERIES = {
    // Total service orders
    totalOrders: `
    SELECT COUNT(*) AS TotalOS
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
  `,
    // Average service time
    averageServiceTime: `
    SELECT
      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS TempoMedioAtendimentoMin
    FROM dado.ArqOrdemServico OS
    WHERE OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
      AND OS.Data >= DATEADD('day', -30, CURRENT_DATE)
  `,
    // Service volume trend (last 30 days)
    volumeTrend: `
    SELECT
      OS.Data,
      COUNT(*) AS QtdeOS
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
    GROUP BY OS.Data
    ORDER BY OS.Data
  `,
    // Service distribution by type
    serviceTypes: `
    SELECT
      OS.TipoAtendimento,
      COUNT(*) AS QtdeOS,
      COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() AS PercOS
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND OS.TipoAtendimento IS NOT NULL
    GROUP BY OS.TipoAtendimento
    ORDER BY QtdeOS DESC
  `,
    // Service time distribution by hour
    timeDistribution: `
    SELECT
      DATEPART('hour', OS.HoraInicial) AS Hora,
      COUNT(*) AS QtdeOS
    FROM dado.ArqOrdemServico OS
    WHERE OS.HoraInicial IS NOT NULL
      AND OS.Data >= DATEADD('day', -30, CURRENT_DATE)
    GROUP BY DATEPART('hour', OS.HoraInicial)
    ORDER BY Hora
  `,
    // Weekly patterns
    weeklyPattern: `
    SELECT
      DATEPART('weekday', OS.Data) AS DiaSemana,
      COUNT(*) AS QtdeOS
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
    GROUP BY DATEPART('weekday', OS.Data)
    ORDER BY DiaSemana
  `,
    // Online vs Presential
    onlineVsPresential: `
    SELECT
      CASE 
        WHEN OS.DisponivelWeb = 1 THEN 'Online'
        ELSE 'Presencial'
      END AS TipoAgendamento,
      COUNT(*) AS QtdeOS,
      COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() AS PercOS
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
    GROUP BY OS.DisponivelWeb
  `,
    // Staff performance
    staffPerformance: `
    SELECT
      OS.Recepcionista AS ID_Recepcionista,
      COUNT(*) AS QtdeOS,
      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS TempoMedioMin
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND OS.Recepcionista IS NOT NULL
      AND OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
    GROUP BY OS.Recepcionista
    ORDER BY QtdeOS DESC
    FETCH FIRST 10 ROWS ONLY
  `,
    // Reprint rate
    reprintRate: `
    SELECT
      COUNT(*) * 100.0 / (
        SELECT COUNT(*) 
        FROM dado.ArqOrdemServico 
        WHERE Data >= DATEADD('day', -30, CURRENT_DATE)
      ) AS TaxaReimpressaoPerc
    FROM dado.ArqOrdemServico OS
    WHERE OS.ReimpressaoData IS NOT NULL
      AND OS.Data >= DATEADD('day', -30, CURRENT_DATE)
  `
};
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        // Execute all service analytics queries
        const [totalOrders, averageServiceTime, volumeTrend, serviceTypes, timeDistribution, weeklyPattern, onlineVsPresential, staffPerformance, reprintRate] = await Promise.all([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(SERVICE_QUERIES.totalOrders),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(SERVICE_QUERIES.averageServiceTime),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(SERVICE_QUERIES.volumeTrend),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(SERVICE_QUERIES.serviceTypes),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(SERVICE_QUERIES.timeDistribution),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(SERVICE_QUERIES.weeklyPattern),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(SERVICE_QUERIES.onlineVsPresential),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(SERVICE_QUERIES.staffPerformance),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(SERVICE_QUERIES.reprintRate)
        ]);
        // Format the response
        const response = {
            success: true,
            timestamp: new Date().toISOString(),
            data: {
                metrics: {
                    totalOrders: totalOrders[0]?.TotalOS || 0,
                    averageServiceTime: Math.round(averageServiceTime[0]?.TempoMedioAtendimentoMin || 0),
                    reprintRate: parseFloat((reprintRate[0]?.TaxaReimpressaoPerc || 0).toFixed(2))
                },
                trends: {
                    volume: volumeTrend.map((row)=>({
                            date: row.Data,
                            orders: parseInt(row.QtdeOS)
                        })),
                    timeDistribution: timeDistribution.map((row)=>({
                            hour: parseInt(row.Hora),
                            orders: parseInt(row.QtdeOS)
                        })),
                    weeklyPattern: weeklyPattern.map((row)=>({
                            dayOfWeek: parseInt(row.DiaSemana),
                            orders: parseInt(row.QtdeOS)
                        }))
                },
                distribution: {
                    serviceTypes: serviceTypes.map((row)=>({
                            type: row.TipoAtendimento,
                            count: parseInt(row.QtdeOS),
                            percentage: parseFloat(row.PercOS.toFixed(2))
                        })),
                    onlineVsPresential: onlineVsPresential.map((row)=>({
                            type: row.TipoAgendamento,
                            count: parseInt(row.QtdeOS),
                            percentage: parseFloat(row.PercOS.toFixed(2))
                        }))
                },
                staff: staffPerformance.map((row)=>({
                        id: row.ID_Recepcionista,
                        orders: parseInt(row.QtdeOS),
                        averageTime: Math.round(row.TempoMedioMin || 0)
                    }))
            }
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response);
    } catch (error) {
        console.error('Error fetching service analytics:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to fetch service analytics',
            message: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__2a6187eb._.js.map