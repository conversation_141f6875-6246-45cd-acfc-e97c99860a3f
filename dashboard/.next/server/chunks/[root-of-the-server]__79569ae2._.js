module.exports = {

"[project]/.next-internal/server/app/api/customer-service/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/odbc [external] (odbc, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("odbc", () => require("odbc"));

module.exports = mod;
}}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "buildDateRangeFilter": (()=>buildDateRangeFilter),
    "closeConnection": (()=>closeConnection),
    "executeQuery": (()=>executeQuery),
    "formatWhatsAppNumber": (()=>formatWhatsAppNumber),
    "getConnection": (()=>getConnection)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$odbc__$5b$external$5d$__$28$odbc$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/odbc [external] (odbc, cjs)");
;
// Database configuration
const dbConfig = {
    host: process.env.IRIS_HOST || 'localhost',
    port: parseInt(process.env.IRIS_PORT || '1972'),
    namespace: process.env.IRIS_NAMESPACE || 'dado',
    user: process.env.IRIS_USER || '_SYSTEM',
    password: process.env.IRIS_PASSWORD || 'SYS',
    sslMode: parseInt(process.env.IRIS_SSL_MODE || '0')
};
// Build connection string
function buildConnectionString() {
    const driverPath = process.env.IRIS_DRIVER_PATH;
    // If no driver path is specified or is "system", use system driver name
    const driverPart = !driverPath || driverPath === 'system' ? 'DRIVER={InterSystems ODBC35}' : `DRIVER=${driverPath}`;
    return `${driverPart};` + `SERVER=${dbConfig.host};` + `PORT=${dbConfig.port};` + `DATABASE=${dbConfig.namespace};` + `UID=${dbConfig.user};` + `PWD=${dbConfig.password};` + `SSL_MODE=${dbConfig.sslMode};`;
}
async function getConnection() {
    try {
        const connectionString = buildConnectionString();
        const connection = await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$odbc__$5b$external$5d$__$28$odbc$2c$__cjs$29$__["connect"])({
            connectionString,
            connectionTimeout: 30,
            loginTimeout: 30
        });
        console.log('Database connection established');
        return connection;
    } catch (error) {
        console.error('Database connection failed:', error);
        throw error;
    }
}
async function closeConnection(connection) {
    if (connection) {
        try {
            await connection.close();
            console.log('Database connection closed');
        } catch (error) {
            console.error('Error closing connection:', error);
        }
    }
}
async function executeQuery(query, params = []) {
    let connection = null;
    try {
        connection = await getConnection();
        const results = await connection.query(query, params);
        // Convert BigInt to string for JSON serialization
        const processedResults = results.map((row)=>{
            const processedRow = {};
            for (const [key, value] of Object.entries(row)){
                if (typeof value === 'bigint') {
                    processedRow[key] = value.toString();
                } else {
                    processedRow[key] = value;
                }
            }
            return processedRow;
        });
        return processedResults;
    } catch (error) {
        console.error('Query execution failed:', error);
        throw error;
    } finally{
        await closeConnection(connection);
    }
}
function formatWhatsAppNumber(telefone) {
    if (!telefone) return null;
    // Clean number (remove special characters)
    const cleanNumber = telefone.replace(/\D/g, '');
    // Check if it has at least 10 digits (Brazilian format)
    if (cleanNumber.length < 10) return null;
    // Add country code if it doesn't have one (55 for Brazil)
    let formattedNumber = cleanNumber;
    if (!formattedNumber.startsWith('55') && formattedNumber.length <= 11) {
        formattedNumber = '55' + formattedNumber;
    }
    // Check if it's a valid number (12 or 13 digits with country code)
    if (formattedNumber.length === 13 || formattedNumber.length === 12) {
        return formattedNumber;
    }
    return null;
}
function buildDateRangeFilter(startDate, endDate) {
    if (!startDate && !endDate) return '';
    if (startDate && endDate) {
        return `WHERE OS.Data BETWEEN '${startDate}' AND '${endDate}'`;
    } else if (startDate) {
        return `WHERE OS.Data >= '${startDate}'`;
    } else if (endDate) {
        return `WHERE OS.Data <= '${endDate}'`;
    }
    return '';
}
}}),
"[project]/src/app/api/customer-service/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
// Customer service analytics queries
const CUSTOMER_SERVICE_QUERIES = {
    // Support ticket metrics (using OS data as proxy)
    ticketMetrics: `
    SELECT
      COUNT(*) AS TotalTickets,
      COUNT(CASE WHEN OS.OsStatus = 1 THEN 1 END) AS ResolvedTickets,
      COUNT(CASE WHEN OS.OsStatus = 0 THEN 1 END) AS CancelledTickets,
      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS AvgResolutionTimeMin
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
  `,
    // Response time distribution
    responseTimeDistribution: `
    SELECT
      CASE
        WHEN DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal) <= 15 THEN '0-15 min'
        WHEN DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal) <= 30 THEN '16-30 min'
        WHEN DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal) <= 60 THEN '31-60 min'
        ELSE '60+ min'
      END AS TimeRange,
      COUNT(*) AS Count
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
    GROUP BY 1
    ORDER BY 
      CASE TimeRange
        WHEN '0-15 min' THEN 1
        WHEN '16-30 min' THEN 2
        WHEN '31-60 min' THEN 3
        ELSE 4
      END
  `,
    // Daily ticket volume
    dailyTicketVolume: `
    SELECT
      OS.Data,
      COUNT(*) AS TicketCount,
      COUNT(CASE WHEN OS.OsStatus = 1 THEN 1 END) AS ResolvedCount,
      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS AvgTimeMin
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
    GROUP BY OS.Data
    ORDER BY OS.Data
  `,
    // Channel distribution (online vs presential)
    channelDistribution: `
    SELECT
      CASE 
        WHEN OS.DisponivelWeb = 1 THEN 'Online'
        ELSE 'Presencial'
      END AS Channel,
      COUNT(*) AS Count,
      COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() AS Percentage
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
    GROUP BY OS.DisponivelWeb
  `,
    // Agent performance
    agentPerformance: `
    SELECT
      OS.Recepcionista AS AgentId,
      COUNT(*) AS TicketsHandled,
      COUNT(CASE WHEN OS.OsStatus = 1 THEN 1 END) AS TicketsResolved,
      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS AvgResolutionTimeMin,
      COUNT(CASE WHEN OS.OsStatus = 1 THEN 1 END) * 100.0 / COUNT(*) AS ResolutionRate
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND OS.Recepcionista IS NOT NULL
      AND OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
    GROUP BY OS.Recepcionista
    ORDER BY TicketsHandled DESC
    FETCH FIRST 10 ROWS ONLY
  `,
    // Service type distribution
    serviceTypeDistribution: `
    SELECT
      COALESCE(OS.TipoAtendimento, 'Não especificado') AS ServiceType,
      COUNT(*) AS Count,
      COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() AS Percentage
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
    GROUP BY OS.TipoAtendimento
    ORDER BY Count DESC
  `
};
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        // Execute all customer service analytics queries
        const [ticketMetrics, responseTimeDistribution, dailyTicketVolume, channelDistribution, agentPerformance, serviceTypeDistribution] = await Promise.all([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(CUSTOMER_SERVICE_QUERIES.ticketMetrics),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(CUSTOMER_SERVICE_QUERIES.responseTimeDistribution),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(CUSTOMER_SERVICE_QUERIES.dailyTicketVolume),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(CUSTOMER_SERVICE_QUERIES.channelDistribution),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(CUSTOMER_SERVICE_QUERIES.agentPerformance),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(CUSTOMER_SERVICE_QUERIES.serviceTypeDistribution)
        ]);
        // Calculate derived metrics
        const totalTickets = ticketMetrics[0]?.TotalTickets || 0;
        const resolvedTickets = ticketMetrics[0]?.ResolvedTickets || 0;
        const avgResolutionTime = Math.round(ticketMetrics[0]?.AvgResolutionTimeMin || 0);
        const resolutionRate = totalTickets > 0 ? resolvedTickets / totalTickets * 100 : 0;
        // Mock satisfaction data (would come from surveys)
        const satisfactionScore = 4.3;
        const satisfactionTrend = [
            4.1,
            4.2,
            4.3,
            4.4,
            4.3,
            4.2,
            4.3
        ];
        // Format the response
        const response = {
            success: true,
            timestamp: new Date().toISOString(),
            data: {
                metrics: {
                    totalTickets,
                    resolvedTickets,
                    avgResolutionTime,
                    resolutionRate: parseFloat(resolutionRate.toFixed(1)),
                    satisfactionScore,
                    firstContactResolution: 78.5 // Mock data
                },
                trends: {
                    volume: dailyTicketVolume.map((row)=>({
                            date: row.Data,
                            total: parseInt(row.TicketCount),
                            resolved: parseInt(row.ResolvedCount),
                            avgTime: Math.round(row.AvgTimeMin || 0)
                        })),
                    satisfaction: satisfactionTrend.map((score, index)=>({
                            week: `Week ${index + 1}`,
                            score
                        }))
                },
                distribution: {
                    responseTime: responseTimeDistribution.map((row)=>({
                            range: row.TimeRange,
                            count: parseInt(row.Count)
                        })),
                    channels: channelDistribution.map((row)=>({
                            channel: row.Channel,
                            count: parseInt(row.Count),
                            percentage: parseFloat(row.Percentage.toFixed(1))
                        })),
                    serviceTypes: serviceTypeDistribution.map((row)=>({
                            type: row.ServiceType,
                            count: parseInt(row.Count),
                            percentage: parseFloat(row.Percentage.toFixed(1))
                        }))
                },
                agents: agentPerformance.map((row, index)=>{
                    // Mock agent names for demo
                    const agentNames = [
                        'Ana Silva',
                        'Carlos Santos',
                        'Maria Costa',
                        'João Oliveira',
                        'Paula Lima',
                        'Roberto Silva',
                        'Fernanda Cruz',
                        'Lucas Pereira',
                        'Juliana Alves',
                        'Pedro Rocha'
                    ];
                    return {
                        id: row.AgentId,
                        name: agentNames[index] || `Agent ${row.AgentId}`,
                        ticketsHandled: parseInt(row.TicketsHandled),
                        ticketsResolved: parseInt(row.TicketsResolved),
                        avgResolutionTime: Math.round(row.AvgResolutionTimeMin || 0),
                        resolutionRate: parseFloat((row.ResolutionRate || 0).toFixed(1)),
                        satisfactionScore: 4.0 + Math.random() * 1.0 // Mock satisfaction
                    };
                }),
                recentTickets: [
                    // Mock recent tickets for demo
                    {
                        id: 'T001',
                        subject: 'Problema com agendamento online',
                        status: 'open',
                        priority: 'high',
                        agent: 'Ana Silva',
                        customer: 'João Santos',
                        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                        channel: 'email'
                    },
                    {
                        id: 'T002',
                        subject: 'Dúvida sobre resultado de exame',
                        status: 'resolved',
                        priority: 'medium',
                        agent: 'Carlos Santos',
                        customer: 'Maria Oliveira',
                        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
                        channel: 'phone'
                    },
                    {
                        id: 'T003',
                        subject: 'Solicitação de segunda via',
                        status: 'in_progress',
                        priority: 'low',
                        agent: 'Maria Costa',
                        customer: 'Pedro Silva',
                        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
                        channel: 'whatsapp'
                    }
                ]
            }
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response);
    } catch (error) {
        console.error('Error fetching customer service analytics:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to fetch customer service analytics',
            message: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__79569ae2._.js.map