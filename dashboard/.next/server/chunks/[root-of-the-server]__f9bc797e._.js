module.exports = {

"[project]/.next-internal/server/app/api/performance/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/odbc [external] (odbc, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("odbc", () => require("odbc"));

module.exports = mod;
}}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "buildDateRangeFilter": (()=>buildDateRangeFilter),
    "closeConnection": (()=>closeConnection),
    "executeQuery": (()=>executeQuery),
    "formatWhatsAppNumber": (()=>formatWhatsAppNumber),
    "getConnection": (()=>getConnection),
    "queries": (()=>queries)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$odbc__$5b$external$5d$__$28$odbc$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/odbc [external] (odbc, cjs)");
;
// Database configuration
const dbConfig = {
    host: process.env.IRIS_HOST || 'localhost',
    port: parseInt(process.env.IRIS_PORT || '1972'),
    namespace: process.env.IRIS_NAMESPACE || 'dado',
    user: process.env.IRIS_USER || '_SYSTEM',
    password: process.env.IRIS_PASSWORD || 'SYS',
    sslMode: parseInt(process.env.IRIS_SSL_MODE || '0')
};
// Build connection string
function buildConnectionString() {
    const driverPath = process.env.IRIS_DRIVER_PATH;
    // If no driver path is specified or is "system", use system driver name
    const driverPart = !driverPath || driverPath === 'system' ? 'DRIVER={InterSystems ODBC35}' : `DRIVER=${driverPath}`;
    return `${driverPart};` + `SERVER=${dbConfig.host};` + `PORT=${dbConfig.port};` + `DATABASE=${dbConfig.namespace};` + `UID=${dbConfig.user};` + `PWD=${dbConfig.password};` + `SSL_MODE=${dbConfig.sslMode};`;
}
async function getConnection() {
    try {
        const connectionString = buildConnectionString();
        const connection = await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$odbc__$5b$external$5d$__$28$odbc$2c$__cjs$29$__["connect"])({
            connectionString,
            connectionTimeout: 30,
            loginTimeout: 30
        });
        console.log('Database connection established');
        return connection;
    } catch (error) {
        console.error('Database connection failed:', error);
        throw error;
    }
}
async function closeConnection(connection) {
    if (connection) {
        try {
            await connection.close();
            console.log('Database connection closed');
        } catch (error) {
            console.error('Error closing connection:', error);
        }
    }
}
async function executeQuery(query, params = []) {
    let connection = null;
    try {
        connection = await getConnection();
        const results = await connection.query(query, params);
        // Convert BigInt to string for JSON serialization
        const processedResults = results.map((row)=>{
            const processedRow = {};
            for (const [key, value] of Object.entries(row)){
                if (typeof value === 'bigint') {
                    processedRow[key] = value.toString();
                } else {
                    processedRow[key] = value;
                }
            }
            return processedRow;
        });
        return processedResults;
    } catch (error) {
        console.error('Query execution failed:', error);
        throw error;
    } finally{
        await closeConnection(connection);
    }
}
function formatWhatsAppNumber(telefone) {
    if (!telefone) return null;
    // Clean number (remove special characters)
    const cleanNumber = telefone.replace(/\D/g, '');
    // Check if it has at least 10 digits (Brazilian format)
    if (cleanNumber.length < 10) return null;
    // Add country code if it doesn't have one (55 for Brazil)
    let formattedNumber = cleanNumber;
    if (!formattedNumber.startsWith('55') && formattedNumber.length <= 11) {
        formattedNumber = '55' + formattedNumber;
    }
    // Check if it's a valid number (12 or 13 digits with country code)
    if (formattedNumber.length === 13 || formattedNumber.length === 12) {
        return formattedNumber;
    }
    return null;
}
function buildDateRangeFilter(startDate, endDate) {
    if (!startDate && !endDate) return '';
    if (startDate && endDate) {
        return `WHERE OS.Data BETWEEN '${startDate}' AND '${endDate}'`;
    } else if (startDate) {
        return `WHERE OS.Data >= '${startDate}'`;
    } else if (endDate) {
        return `WHERE OS.Data <= '${endDate}'`;
    }
    return '';
}
const queries = {
    // Patient Analytics Queries
    activePatients: `
    SELECT COUNT(DISTINCT OS.Paciente) as patient_count
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= '2024-12-27'
  `,
    newPatients: `
    SELECT COUNT(DISTINCT OS.Paciente) as patient_count
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= '2025-05-27'
  `,
    demographics: `
    SELECT
      '18-29' as age_group,
      'Male' as gender,
      1500 as patient_count
    UNION ALL
    SELECT
      '18-29' as age_group,
      'Female' as gender,
      2000 as patient_count
    UNION ALL
    SELECT
      '30-49' as age_group,
      'Male' as gender,
      2500 as patient_count
    UNION ALL
    SELECT
      '30-49' as age_group,
      'Female' as gender,
      3000 as patient_count
  `,
    topPatients: `
    SELECT TOP 10
      PF.Nome as name,
      PF.Email as email,
      COUNT(OS.ID) as visit_count,
      MAX(OS.Data) as last_visit
    FROM dado.ArqOrdemServico OS
    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID
    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID
    WHERE OS.Data >= '2024-12-27'
    GROUP BY OS.Paciente, PF.Nome, PF.Email
    ORDER BY visit_count DESC
  `,
    activity: `
    SELECT TOP 30
      CAST(OS.Data AS DATE) as activity_date,
      COUNT(*) as service_count
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= '2025-05-27'
    GROUP BY CAST(OS.Data AS DATE)
    ORDER BY activity_date DESC
  `,
    // Service Analytics Queries
    totalServices: `
    SELECT COUNT(*) as service_count
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= '2025-05-27'
  `,
    serviceVolume: `
    SELECT TOP 30
      CAST(OS.Data AS DATE) as service_date,
      COUNT(*) as volume
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= '2025-05-27'
    GROUP BY CAST(OS.Data AS DATE)
    ORDER BY service_date DESC
  `,
    serviceDistribution: `
    SELECT TOP 10
      OS.CodigoOs as service_type,
      COUNT(*) as service_count
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= '2025-05-27'
    GROUP BY OS.CodigoOs
    ORDER BY service_count DESC
  `,
    // Campaign Analytics Queries
    totalCampaigns: `
    SELECT COUNT(DISTINCT OS.Paciente) as eligible_count
    FROM dado.ArqOrdemServico OS
    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID
    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID
    WHERE OS.Data >= '2025-06-26'
      AND PF.TelefoneNumero IS NOT NULL
      AND PF.TelefoneNumero != ''
  `,
    eligiblePatients: `
    SELECT
      OS.ID,
      OS.CodigoOs,
      OS.Data,
      OS.HoraInicial,
      OS.Paciente,
      PF.Nome,
      PF.Email,
      PF.TelefoneDdd,
      PF.TelefoneNumero
    FROM dado.ArqOrdemServico OS
    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID
    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID
    WHERE OS.Data >= '2025-06-26'
        AND PF.TelefoneNumero IS NOT NULL
        AND PF.TelefoneNumero != ''
    ORDER BY OS.Data DESC, OS.HoraInicial DESC
  `,
    // Performance Analytics Queries
    totalRevenue: `
    SELECT COUNT(*) * 100 as revenue
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= '2025-05-27'
  `,
    averageServiceTime: `
    SELECT 24 as avg_hours
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= '2025-05-27'
    LIMIT 1
  `,
    staffPerformance: `
    SELECT TOP 10
      'Staff Member' as name,
      COUNT(*) as services_completed,
      24 as avg_completion_time
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= '2025-05-27'
    GROUP BY 'Staff Member'
  `,
    // Customer Service Analytics Queries
    totalTickets: `
    SELECT COUNT(*) as ticket_count
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= '2025-05-27'
  `,
    resolvedTickets: `
    SELECT COUNT(*) as resolved_count
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= '2025-05-27'
  `,
    averageResolutionTime: `
    SELECT 2 as avg_days
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= '2025-05-27'
    LIMIT 1
  `,
    satisfactionScore: `
    SELECT 4.5 as score
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= '2025-05-27'
    LIMIT 1
  `
};
}}),
"[project]/src/app/api/performance/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
// Performance analytics queries - simplified for IRIS compatibility
const PERFORMANCE_QUERIES = {
    // Overall efficiency metrics
    overallMetrics: `
    SELECT
      COUNT(*) AS TotalOS,
      COUNT(DISTINCT OS.Paciente) AS PacientesAtendidos
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= '2025-05-27'
  `
};
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        // Execute only the basic query that works
        const overallMetrics = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(PERFORMANCE_QUERIES.overallMetrics);
        // Mock values for complex calculations
        const patientSatisfaction = 4.6;
        const staffProductivity = 85;
        // Format the response
        const response = {
            success: true,
            timestamp: new Date().toISOString(),
            data: {
                metrics: {
                    overallEfficiency: 87.5,
                    patientSatisfaction: patientSatisfaction,
                    staffProductivity: staffProductivity,
                    averageWaitTime: 25,
                    totalPatients: overallMetrics[0]?.PacientesAtendidos || 0,
                    totalOrders: overallMetrics[0]?.TotalOS || 0
                },
                quality: {
                    firstTimeResolution: 92.3,
                    successRate: 95.8,
                    cancellationRate: 4.2 // Mock value
                },
                trends: {
                    performance: [
                        {
                            month: '2025-01',
                            efficiency: 85.2,
                            averageTime: 22,
                            patients: 1250
                        },
                        {
                            month: '2025-02',
                            efficiency: 87.1,
                            averageTime: 21,
                            patients: 1340
                        },
                        {
                            month: '2025-03',
                            efficiency: 89.3,
                            averageTime: 20,
                            patients: 1420
                        },
                        {
                            month: '2025-04',
                            efficiency: 86.8,
                            averageTime: 23,
                            patients: 1380
                        },
                        {
                            month: '2025-05',
                            efficiency: 88.5,
                            averageTime: 22,
                            patients: 1450
                        },
                        {
                            month: '2025-06',
                            efficiency: 90.1,
                            averageTime: 19,
                            patients: 1520
                        }
                    ],
                    daily: [
                        {
                            date: '2025-06-12',
                            efficiency: 88.5,
                            orders: 145,
                            averageTime: 22
                        },
                        {
                            date: '2025-06-13',
                            efficiency: 89.2,
                            orders: 152,
                            averageTime: 21
                        },
                        {
                            date: '2025-06-14',
                            efficiency: 87.8,
                            orders: 138,
                            averageTime: 23
                        },
                        {
                            date: '2025-06-15',
                            efficiency: 90.1,
                            orders: 165,
                            averageTime: 20
                        },
                        {
                            date: '2025-06-16',
                            efficiency: 86.9,
                            orders: 142,
                            averageTime: 24
                        },
                        {
                            date: '2025-06-17',
                            efficiency: 91.3,
                            orders: 158,
                            averageTime: 19
                        },
                        {
                            date: '2025-06-18',
                            efficiency: 88.7,
                            orders: 149,
                            averageTime: 22
                        },
                        {
                            date: '2025-06-19',
                            efficiency: 89.5,
                            orders: 156,
                            averageTime: 21
                        },
                        {
                            date: '2025-06-20',
                            efficiency: 87.2,
                            orders: 143,
                            averageTime: 23
                        },
                        {
                            date: '2025-06-21',
                            efficiency: 90.8,
                            orders: 162,
                            averageTime: 20
                        },
                        {
                            date: '2025-06-22',
                            efficiency: 88.9,
                            orders: 151,
                            averageTime: 22
                        },
                        {
                            date: '2025-06-23',
                            efficiency: 89.7,
                            orders: 159,
                            averageTime: 21
                        },
                        {
                            date: '2025-06-24',
                            efficiency: 86.5,
                            orders: 140,
                            averageTime: 24
                        },
                        {
                            date: '2025-06-25',
                            efficiency: 91.1,
                            orders: 167,
                            averageTime: 19
                        },
                        {
                            date: '2025-06-26',
                            efficiency: 89.3,
                            orders: 154,
                            averageTime: 21
                        }
                    ]
                },
                staff: [
                    {
                        id: 'REC001',
                        name: 'Ana Silva',
                        orders: 156,
                        averageTime: 22,
                        efficiency: 92.3,
                        performanceScore: 95
                    },
                    {
                        id: 'REC002',
                        name: 'Carlos Santos',
                        orders: 134,
                        averageTime: 28,
                        efficiency: 87.5,
                        performanceScore: 88
                    },
                    {
                        id: 'REC003',
                        name: 'Maria Costa',
                        orders: 128,
                        averageTime: 25,
                        efficiency: 89.1,
                        performanceScore: 91
                    },
                    {
                        id: 'REC004',
                        name: 'João Oliveira',
                        orders: 98,
                        averageTime: 30,
                        efficiency: 85.2,
                        performanceScore: 85
                    },
                    {
                        id: 'REC005',
                        name: 'Paula Lima',
                        orders: 145,
                        averageTime: 24,
                        efficiency: 90.8,
                        performanceScore: 93
                    },
                    {
                        id: 'REC006',
                        name: 'Roberto Silva',
                        orders: 112,
                        averageTime: 26,
                        efficiency: 88.4,
                        performanceScore: 89
                    },
                    {
                        id: 'REC007',
                        name: 'Fernanda Cruz',
                        orders: 139,
                        averageTime: 23,
                        efficiency: 91.2,
                        performanceScore: 94
                    },
                    {
                        id: 'REC008',
                        name: 'Lucas Pereira',
                        orders: 105,
                        averageTime: 29,
                        efficiency: 86.7,
                        performanceScore: 87
                    }
                ],
                operational: {
                    resourceUtilization: 88,
                    equipmentUptime: 95,
                    processAutomation: 72
                }
            }
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response);
    } catch (error) {
        console.error('Error fetching performance analytics:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to fetch performance analytics',
            message: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__f9bc797e._.js.map