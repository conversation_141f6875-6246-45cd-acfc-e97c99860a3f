module.exports = {

"[project]/.next-internal/server/app/api/performance/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/odbc [external] (odbc, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("odbc", () => require("odbc"));

module.exports = mod;
}}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "buildDateRangeFilter": (()=>buildDateRangeFilter),
    "closeConnection": (()=>closeConnection),
    "executeQuery": (()=>executeQuery),
    "formatWhatsAppNumber": (()=>formatWhatsAppNumber),
    "getConnection": (()=>getConnection)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$odbc__$5b$external$5d$__$28$odbc$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/odbc [external] (odbc, cjs)");
;
// Database configuration
const dbConfig = {
    host: process.env.IRIS_HOST || 'localhost',
    port: parseInt(process.env.IRIS_PORT || '1972'),
    namespace: process.env.IRIS_NAMESPACE || 'dado',
    user: process.env.IRIS_USER || '_SYSTEM',
    password: process.env.IRIS_PASSWORD || 'SYS',
    sslMode: parseInt(process.env.IRIS_SSL_MODE || '0')
};
// Build connection string
function buildConnectionString() {
    const driverPath = process.env.IRIS_DRIVER_PATH;
    // If no driver path is specified or is "system", use system driver name
    const driverPart = !driverPath || driverPath === 'system' ? 'DRIVER={InterSystems ODBC35}' : `DRIVER=${driverPath}`;
    return `${driverPart};` + `SERVER=${dbConfig.host};` + `PORT=${dbConfig.port};` + `DATABASE=${dbConfig.namespace};` + `UID=${dbConfig.user};` + `PWD=${dbConfig.password};` + `SSL_MODE=${dbConfig.sslMode};`;
}
async function getConnection() {
    try {
        const connectionString = buildConnectionString();
        const connection = await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$odbc__$5b$external$5d$__$28$odbc$2c$__cjs$29$__["connect"])({
            connectionString,
            connectionTimeout: 30,
            loginTimeout: 30
        });
        console.log('Database connection established');
        return connection;
    } catch (error) {
        console.error('Database connection failed:', error);
        throw error;
    }
}
async function closeConnection(connection) {
    if (connection) {
        try {
            await connection.close();
            console.log('Database connection closed');
        } catch (error) {
            console.error('Error closing connection:', error);
        }
    }
}
async function executeQuery(query, params = []) {
    let connection = null;
    try {
        connection = await getConnection();
        const results = await connection.query(query, params);
        // Convert BigInt to string for JSON serialization
        const processedResults = results.map((row)=>{
            const processedRow = {};
            for (const [key, value] of Object.entries(row)){
                if (typeof value === 'bigint') {
                    processedRow[key] = value.toString();
                } else {
                    processedRow[key] = value;
                }
            }
            return processedRow;
        });
        return processedResults;
    } catch (error) {
        console.error('Query execution failed:', error);
        throw error;
    } finally{
        await closeConnection(connection);
    }
}
function formatWhatsAppNumber(telefone) {
    if (!telefone) return null;
    // Clean number (remove special characters)
    const cleanNumber = telefone.replace(/\D/g, '');
    // Check if it has at least 10 digits (Brazilian format)
    if (cleanNumber.length < 10) return null;
    // Add country code if it doesn't have one (55 for Brazil)
    let formattedNumber = cleanNumber;
    if (!formattedNumber.startsWith('55') && formattedNumber.length <= 11) {
        formattedNumber = '55' + formattedNumber;
    }
    // Check if it's a valid number (12 or 13 digits with country code)
    if (formattedNumber.length === 13 || formattedNumber.length === 12) {
        return formattedNumber;
    }
    return null;
}
function buildDateRangeFilter(startDate, endDate) {
    if (!startDate && !endDate) return '';
    if (startDate && endDate) {
        return `WHERE OS.Data BETWEEN '${startDate}' AND '${endDate}'`;
    } else if (startDate) {
        return `WHERE OS.Data >= '${startDate}'`;
    } else if (endDate) {
        return `WHERE OS.Data <= '${endDate}'`;
    }
    return '';
}
}}),
"[project]/src/app/api/performance/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
// Performance analytics queries
const PERFORMANCE_QUERIES = {
    // Overall efficiency metrics
    overallMetrics: `
    SELECT
      COUNT(*) AS TotalOS,
      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS TempoMedioMin,
      COUNT(CASE WHEN OS.ReimpressaoData IS NULL THEN 1 END) * 100.0 / COUNT(*) AS EficienciaPerc,
      COUNT(DISTINCT OS.Paciente) AS PacientesAtendidos
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
  `,
    // Staff performance
    staffPerformance: `
    SELECT
      OS.Recepcionista AS StaffId,
      COUNT(*) AS QtdeOS,
      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS TempoMedioMin,
      COUNT(CASE WHEN OS.ReimpressaoData IS NULL THEN 1 END) * 100.0 / COUNT(*) AS EficienciaPerc
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
      AND OS.Recepcionista IS NOT NULL
      AND OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
    GROUP BY OS.Recepcionista
    ORDER BY QtdeOS DESC
  `,
    // Performance trend over time (last 6 months)
    performanceTrend: `
    SELECT
      YEAR(OS.Data) AS Ano,
      MONTH(OS.Data) AS Mes,
      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS TempoMedioMin,
      COUNT(CASE WHEN OS.ReimpressaoData IS NULL THEN 1 END) * 100.0 / COUNT(*) AS EficienciaPerc,
      COUNT(DISTINCT OS.Paciente) AS PacientesAtendidos
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('month', -6, CURRENT_DATE)
      AND OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
    GROUP BY YEAR(OS.Data), MONTH(OS.Data)
    ORDER BY Ano, Mes
  `,
    // Daily efficiency trend (last 15 days)
    dailyEfficiency: `
    SELECT
      OS.Data,
      COUNT(*) AS QtdeOS,
      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS TempoMedioMin,
      COUNT(CASE WHEN OS.ReimpressaoData IS NULL THEN 1 END) * 100.0 / COUNT(*) AS EficienciaPerc
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -15, CURRENT_DATE)
      AND OS.HoraInicial IS NOT NULL 
      AND OS.HoraFinal IS NOT NULL
    GROUP BY OS.Data
    ORDER BY OS.Data
  `,
    // Service quality metrics
    qualityMetrics: `
    SELECT
      COUNT(CASE WHEN OS.ReimpressaoData IS NULL THEN 1 END) * 100.0 / COUNT(*) AS PrimeiraResolucaoPerc,
      COUNT(CASE WHEN OS.OsStatus = 1 THEN 1 END) * 100.0 / COUNT(*) AS TaxaSucessoPerc,
      COUNT(CASE WHEN OS.OsStatus = 0 THEN 1 END) * 100.0 / COUNT(*) AS TaxaCancelamentoPerc
    FROM dado.ArqOrdemServico OS
    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)
  `
};
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        // Execute all performance analytics queries
        const [overallMetrics, staffPerformance, performanceTrend, dailyEfficiency, qualityMetrics] = await Promise.all([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(PERFORMANCE_QUERIES.overallMetrics),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(PERFORMANCE_QUERIES.staffPerformance),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(PERFORMANCE_QUERIES.performanceTrend),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(PERFORMANCE_QUERIES.dailyEfficiency),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(PERFORMANCE_QUERIES.qualityMetrics)
        ]);
        // Calculate overall efficiency score
        const efficiency = overallMetrics[0]?.EficienciaPerc || 0;
        const avgServiceTime = overallMetrics[0]?.TempoMedioMin || 0;
        const qualityScore = qualityMetrics[0]?.PrimeiraResolucaoPerc || 0;
        // Mock patient satisfaction (would come from a separate survey system)
        const patientSatisfaction = 4.6;
        const staffProductivity = Math.min(100, Math.max(0, 100 - (avgServiceTime - 10) * 2));
        // Format the response
        const response = {
            success: true,
            timestamp: new Date().toISOString(),
            data: {
                metrics: {
                    overallEfficiency: parseFloat(efficiency.toFixed(1)),
                    patientSatisfaction: patientSatisfaction,
                    staffProductivity: parseFloat(staffProductivity.toFixed(1)),
                    averageWaitTime: Math.round(avgServiceTime),
                    totalPatients: overallMetrics[0]?.PacientesAtendidos || 0,
                    totalOrders: overallMetrics[0]?.TotalOS || 0
                },
                quality: {
                    firstTimeResolution: parseFloat((qualityMetrics[0]?.PrimeiraResolucaoPerc || 0).toFixed(1)),
                    successRate: parseFloat((qualityMetrics[0]?.TaxaSucessoPerc || 0).toFixed(1)),
                    cancellationRate: parseFloat((qualityMetrics[0]?.TaxaCancelamentoPerc || 0).toFixed(1))
                },
                trends: {
                    performance: performanceTrend.map((row)=>({
                            month: `${row.Ano}-${String(row.Mes).padStart(2, '0')}`,
                            efficiency: parseFloat((row.EficienciaPerc || 0).toFixed(1)),
                            averageTime: Math.round(row.TempoMedioMin || 0),
                            patients: parseInt(row.PacientesAtendidos)
                        })),
                    daily: dailyEfficiency.map((row)=>({
                            date: row.Data,
                            efficiency: parseFloat((row.EficienciaPerc || 0).toFixed(1)),
                            orders: parseInt(row.QtdeOS),
                            averageTime: Math.round(row.TempoMedioMin || 0)
                        }))
                },
                staff: staffPerformance.map((row, index)=>{
                    // Mock staff names for demo
                    const staffNames = [
                        'Ana Silva',
                        'Carlos Santos',
                        'Maria Costa',
                        'João Oliveira',
                        'Paula Lima',
                        'Roberto Silva',
                        'Fernanda Cruz',
                        'Lucas Pereira'
                    ];
                    const performanceScore = Math.min(100, Math.max(0, (row.EficienciaPerc || 0) * 0.6 + Math.max(0, 100 - (row.TempoMedioMin - 10) * 2) * 0.4));
                    return {
                        id: row.StaffId,
                        name: staffNames[index] || `Staff ${row.StaffId}`,
                        orders: parseInt(row.QtdeOS),
                        averageTime: Math.round(row.TempoMedioMin || 0),
                        efficiency: parseFloat((row.EficienciaPerc || 0).toFixed(1)),
                        performanceScore: Math.round(performanceScore)
                    };
                }).slice(0, 8),
                operational: {
                    resourceUtilization: 88,
                    equipmentUptime: 95,
                    processAutomation: 72
                }
            }
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response);
    } catch (error) {
        console.error('Error fetching performance analytics:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to fetch performance analytics',
            message: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__f9bc797e._.js.map