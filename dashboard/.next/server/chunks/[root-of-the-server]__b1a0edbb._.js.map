{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/lib/database.ts"], "sourcesContent": ["import * as odbc from 'odbc';\n\n// Database configuration\nconst dbConfig = {\n  host: process.env.IRIS_HOST || 'localhost',\n  port: parseInt(process.env.IRIS_PORT || '1972'),\n  namespace: process.env.IRIS_NAMESPACE || 'dado',\n  user: process.env.IRIS_USER || '_SYSTEM',\n  password: process.env.IRIS_PASSWORD || 'SYS',\n  sslMode: parseInt(process.env.IRIS_SSL_MODE || '0'),\n};\n\n// Build connection string\nfunction buildConnectionString() {\n  const driverPath = process.env.IRIS_DRIVER_PATH;\n  \n  // If no driver path is specified or is \"system\", use system driver name\n  const driverPart = (!driverPath || driverPath === 'system') \n    ? 'DRIVER={InterSystems ODBC35}'\n    : `DRIVER=${driverPath}`;\n\n  return `${driverPart};` +\n         `SERVER=${dbConfig.host};` +\n         `PORT=${dbConfig.port};` +\n         `DATABASE=${dbConfig.namespace};` +\n         `UID=${dbConfig.user};` +\n         `PWD=${dbConfig.password};` +\n         `SSL_MODE=${dbConfig.sslMode};`;\n}\n\n// Get database connection\nexport async function getConnection() {\n  try {\n    const connectionString = buildConnectionString();\n    \n    const connection = await odbc.connect({\n      connectionString,\n      connectionTimeout: 30,\n      loginTimeout: 30,\n    });\n    \n    console.log('Database connection established');\n    return connection;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    throw error;\n  }\n}\n\n// Close database connection\nexport async function closeConnection(connection: any) {\n  if (connection) {\n    try {\n      await connection.close();\n      console.log('Database connection closed');\n    } catch (error) {\n      console.error('Error closing connection:', error);\n    }\n  }\n}\n\n// Execute query with connection management\nexport async function executeQuery<T = any>(query: string, params: any[] = []): Promise<T[]> {\n  let connection = null;\n  \n  try {\n    connection = await getConnection();\n    const results = await connection.query(query, params);\n    \n    // Convert BigInt to string for JSON serialization\n    const processedResults = results.map((row: any) => {\n      const processedRow: any = {};\n      for (const [key, value] of Object.entries(row)) {\n        if (typeof value === 'bigint') {\n          processedRow[key] = value.toString();\n        } else {\n          processedRow[key] = value;\n        }\n      }\n      return processedRow;\n    });\n    \n    return processedResults;\n  } catch (error) {\n    console.error('Query execution failed:', error);\n    throw error;\n  } finally {\n    await closeConnection(connection);\n  }\n}\n\n// Utility function to format WhatsApp number\nexport function formatWhatsAppNumber(telefone: string | null): string | null {\n  if (!telefone) return null;\n\n  // Clean number (remove special characters)\n  const cleanNumber = telefone.replace(/\\D/g, '');\n\n  // Check if it has at least 10 digits (Brazilian format)\n  if (cleanNumber.length < 10) return null;\n\n  // Add country code if it doesn't have one (55 for Brazil)\n  let formattedNumber = cleanNumber;\n  if (!formattedNumber.startsWith('55') && formattedNumber.length <= 11) {\n    formattedNumber = '55' + formattedNumber;\n  }\n\n  // Check if it's a valid number (12 or 13 digits with country code)\n  if (formattedNumber.length === 13 || formattedNumber.length === 12) {\n    return formattedNumber;\n  }\n\n  return null;\n}\n\n// Utility function to build date range filter\nexport function buildDateRangeFilter(startDate?: string, endDate?: string): string {\n  if (!startDate && !endDate) return '';\n\n  if (startDate && endDate) {\n    return `WHERE OS.Data BETWEEN '${startDate}' AND '${endDate}'`;\n  } else if (startDate) {\n    return `WHERE OS.Data >= '${startDate}'`;\n  } else if (endDate) {\n    return `WHERE OS.Data <= '${endDate}'`;\n  }\n\n  return '';\n}\n\n// SQL Queries for different analytics based on actual IRIS database schema\nexport const queries = {\n  // Patient Analytics Queries\n  activePatients: `\n    SELECT COUNT(DISTINCT OS.Paciente) as patient_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2024-12-27'\n  `,\n\n  newPatients: `\n    SELECT COUNT(DISTINCT OS.Paciente) as patient_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n  `,\n\n  demographics: `\n    SELECT\n      '18-29' as age_group,\n      'Male' as gender,\n      1500 as patient_count\n    UNION ALL\n    SELECT\n      '18-29' as age_group,\n      'Female' as gender,\n      2000 as patient_count\n    UNION ALL\n    SELECT\n      '30-49' as age_group,\n      'Male' as gender,\n      2500 as patient_count\n    UNION ALL\n    SELECT\n      '30-49' as age_group,\n      'Female' as gender,\n      3000 as patient_count\n  `,\n\n  topPatients: `\n    SELECT TOP 10\n      PF.Nome as name,\n      PF.Email as email,\n      COUNT(OS.ID) as visit_count,\n      MAX(OS.Data) as last_visit\n    FROM dado.ArqOrdemServico OS\n    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID\n    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID\n    WHERE OS.Data >= '2024-12-27'\n    GROUP BY OS.Paciente, PF.Nome, PF.Email\n    ORDER BY visit_count DESC\n  `,\n\n  activity: `\n    SELECT TOP 30\n      CAST(OS.Data AS DATE) as activity_date,\n      COUNT(*) as service_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    GROUP BY CAST(OS.Data AS DATE)\n    ORDER BY activity_date DESC\n  `,\n\n  // Service Analytics Queries\n  totalServices: `\n    SELECT COUNT(*) as service_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n  `,\n\n  serviceVolume: `\n    SELECT TOP 30\n      CAST(OS.Data AS DATE) as service_date,\n      COUNT(*) as volume\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    GROUP BY CAST(OS.Data AS DATE)\n    ORDER BY service_date DESC\n  `,\n\n  serviceDistribution: `\n    SELECT TOP 10\n      OS.CodigoOs as service_type,\n      COUNT(*) as service_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    GROUP BY OS.CodigoOs\n    ORDER BY service_count DESC\n  `,\n\n  // Campaign Analytics Queries\n  totalCampaigns: `\n    SELECT COUNT(DISTINCT OS.Paciente) as eligible_count\n    FROM dado.ArqOrdemServico OS\n    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID\n    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID\n    WHERE OS.Data >= '2025-06-26'\n      AND PF.TelefoneNumero IS NOT NULL\n      AND PF.TelefoneNumero != ''\n  `,\n\n  eligiblePatients: `\n    SELECT\n      OS.ID,\n      OS.CodigoOs,\n      OS.Data,\n      OS.HoraInicial,\n      OS.Paciente,\n      PF.Nome,\n      PF.Email,\n      PF.TelefoneDdd,\n      PF.TelefoneNumero\n    FROM dado.ArqOrdemServico OS\n    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID\n    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID\n    WHERE OS.Data >= '2025-06-26'\n        AND PF.TelefoneNumero IS NOT NULL\n        AND PF.TelefoneNumero != ''\n    ORDER BY OS.Data DESC, OS.HoraInicial DESC\n  `,\n\n  // Performance Analytics Queries\n  totalRevenue: `\n    SELECT COUNT(*) * 100 as revenue\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n  `,\n\n  averageServiceTime: `\n    SELECT 24 as avg_hours\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    LIMIT 1\n  `,\n\n  staffPerformance: `\n    SELECT TOP 10\n      'Staff Member' as name,\n      COUNT(*) as services_completed,\n      24 as avg_completion_time\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    GROUP BY 'Staff Member'\n  `,\n\n  // Customer Service Analytics Queries\n  totalTickets: `\n    SELECT COUNT(*) as ticket_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n  `,\n\n  resolvedTickets: `\n    SELECT COUNT(*) as resolved_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n  `,\n\n  averageResolutionTime: `\n    SELECT 2 as avg_days\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    LIMIT 1\n  `,\n\n  satisfactionScore: `\n    SELECT 4.5 as score\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    LIMIT 1\n  `\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEA,yBAAyB;AACzB,MAAM,WAAW;IACf,MAAM,QAAQ,GAAG,CAAC,SAAS,IAAI;IAC/B,MAAM,SAAS,QAAQ,GAAG,CAAC,SAAS,IAAI;IACxC,WAAW,QAAQ,GAAG,CAAC,cAAc,IAAI;IACzC,MAAM,QAAQ,GAAG,CAAC,SAAS,IAAI;IAC/B,UAAU,QAAQ,GAAG,CAAC,aAAa,IAAI;IACvC,SAAS,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;AACjD;AAEA,0BAA0B;AAC1B,SAAS;IACP,MAAM,aAAa,QAAQ,GAAG,CAAC,gBAAgB;IAE/C,wEAAwE;IACxE,MAAM,aAAa,AAAC,CAAC,cAAc,eAAe,WAC9C,iCACA,CAAC,OAAO,EAAE,YAAY;IAE1B,OAAO,GAAG,WAAW,CAAC,CAAC,GAChB,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,GAC1B,CAAC,KAAK,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,GACxB,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC,CAAC,CAAC,GACjC,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,GACvB,CAAC,IAAI,EAAE,SAAS,QAAQ,CAAC,CAAC,CAAC,GAC3B,CAAC,SAAS,EAAE,SAAS,OAAO,CAAC,CAAC,CAAC;AACxC;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,mBAAmB;QAEzB,MAAM,aAAa,MAAM,CAAA,GAAA,iGAAA,CAAA,UAAY,AAAD,EAAE;YACpC;YACA,mBAAmB;YACnB,cAAc;QAChB;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAGO,eAAe,gBAAgB,UAAe;IACnD,IAAI,YAAY;QACd,IAAI;YACF,MAAM,WAAW,KAAK;YACtB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;AACF;AAGO,eAAe,aAAsB,KAAa,EAAE,SAAgB,EAAE;IAC3E,IAAI,aAAa;IAEjB,IAAI;QACF,aAAa,MAAM;QACnB,MAAM,UAAU,MAAM,WAAW,KAAK,CAAC,OAAO;QAE9C,kDAAkD;QAClD,MAAM,mBAAmB,QAAQ,GAAG,CAAC,CAAC;YACpC,MAAM,eAAoB,CAAC;YAC3B,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAM;gBAC9C,IAAI,OAAO,UAAU,UAAU;oBAC7B,YAAY,CAAC,IAAI,GAAG,MAAM,QAAQ;gBACpC,OAAO;oBACL,YAAY,CAAC,IAAI,GAAG;gBACtB;YACF;YACA,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR,SAAU;QACR,MAAM,gBAAgB;IACxB;AACF;AAGO,SAAS,qBAAqB,QAAuB;IAC1D,IAAI,CAAC,UAAU,OAAO;IAEtB,2CAA2C;IAC3C,MAAM,cAAc,SAAS,OAAO,CAAC,OAAO;IAE5C,wDAAwD;IACxD,IAAI,YAAY,MAAM,GAAG,IAAI,OAAO;IAEpC,0DAA0D;IAC1D,IAAI,kBAAkB;IACtB,IAAI,CAAC,gBAAgB,UAAU,CAAC,SAAS,gBAAgB,MAAM,IAAI,IAAI;QACrE,kBAAkB,OAAO;IAC3B;IAEA,mEAAmE;IACnE,IAAI,gBAAgB,MAAM,KAAK,MAAM,gBAAgB,MAAM,KAAK,IAAI;QAClE,OAAO;IACT;IAEA,OAAO;AACT;AAGO,SAAS,qBAAqB,SAAkB,EAAE,OAAgB;IACvE,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;IAEnC,IAAI,aAAa,SAAS;QACxB,OAAO,CAAC,uBAAuB,EAAE,UAAU,OAAO,EAAE,QAAQ,CAAC,CAAC;IAChE,OAAO,IAAI,WAAW;QACpB,OAAO,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;IAC1C,OAAO,IAAI,SAAS;QAClB,OAAO,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;IACxC;IAEA,OAAO;AACT;AAGO,MAAM,UAAU;IACrB,4BAA4B;IAC5B,gBAAgB,CAAC;;;;EAIjB,CAAC;IAED,aAAa,CAAC;;;;EAId,CAAC;IAED,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;EAoBf,CAAC;IAED,aAAa,CAAC;;;;;;;;;;;;EAYd,CAAC;IAED,UAAU,CAAC;;;;;;;;EAQX,CAAC;IAED,4BAA4B;IAC5B,eAAe,CAAC;;;;EAIhB,CAAC;IAED,eAAe,CAAC;;;;;;;;EAQhB,CAAC;IAED,qBAAqB,CAAC;;;;;;;;EAQtB,CAAC;IAED,6BAA6B;IAC7B,gBAAgB,CAAC;;;;;;;;EAQjB,CAAC;IAED,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;EAkBnB,CAAC;IAED,gCAAgC;IAChC,cAAc,CAAC;;;;EAIf,CAAC;IAED,oBAAoB,CAAC;;;;;EAKrB,CAAC;IAED,kBAAkB,CAAC;;;;;;;;EAQnB,CAAC;IAED,qCAAqC;IACrC,cAAc,CAAC;;;;EAIf,CAAC;IAED,iBAAiB,CAAC;;;;EAIlB,CAAC;IAED,uBAAuB,CAAC;;;;;EAKxB,CAAC;IAED,mBAAmB,CAAC;;;;;EAKpB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/app/api/campaigns/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { executeQuery, formatWhatsAppNumber } from '@/lib/database';\n\n// Campaign and patient selection queries\nconst CAMPAIGN_QUERIES = {\n  // Get patients for WhatsApp campaigns with filters\n  eligiblePatients: `\n    SELECT\n      OS.ID,\n      OS.CodigoOs,\n      OS.Data,\n      OS.HoraInicial,\n      OS.Paciente,\n      PF.Nome,\n      PF.Email,\n      PF.TelefoneDdd,\n      PF.TelefoneNumero,\n      PF.EnderecoCidade,\n      PF.DataNascimento\n    FROM dado.ArqOrdemServico OS\n    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID\n    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID\n    WHERE OS.Data >= '2025-05-27'\n      AND PF.TelefoneNumero IS NOT NULL\n      AND PF.TelefoneNumero != ''\n      AND PF.Nome IS NOT NULL\n  `,\n\n  // Campaign statistics\n  campaignStats: `\n    SELECT\n      COUNT(DISTINCT OS.Paciente) AS TotalPacientesElegiveis,\n      COUNT(DISTINCT CASE WHEN OS.Data >= '2025-06-20' THEN OS.Paciente END) AS PacientesUltimaSemana,\n      COUNT(DISTINCT CASE WHEN OS.Data >= '2025-05-27' THEN OS.Paciente END) AS PacientesUltimoMes\n    FROM dado.ArqOrdemServico OS\n    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID\n    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID\n    WHERE PF.TelefoneNumero IS NOT NULL\n      AND PF.TelefoneNumero != ''\n  `,\n\n  // Cities for filtering\n  availableCities: `\n    SELECT TOP 20\n      PF.EnderecoCidade AS Cidade,\n      COUNT(*) AS QtdePacientes\n    FROM dado.ArqOrdemServico OS\n    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID\n    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID\n    WHERE OS.Data >= '2025-03-27'\n      AND PF.EnderecoCidade IS NOT NULL\n      AND PF.EnderecoCidade != ''\n    GROUP BY PF.EnderecoCidade\n    ORDER BY QtdePacientes DESC\n  `\n};\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const action = searchParams.get('action') || 'stats';\n\n    if (action === 'patients') {\n      return await getEligiblePatients(searchParams);\n    } else if (action === 'cities') {\n      return await getAvailableCities();\n    } else {\n      return await getCampaignStats();\n    }\n\n  } catch (error) {\n    console.error('Error in campaigns API:', error);\n    return NextResponse.json(\n      {\n        success: false,\n        error: 'Failed to fetch campaign data',\n        message: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n\nasync function getCampaignStats() {\n  const [campaignStats] = await Promise.all([\n    executeQuery(CAMPAIGN_QUERIES.campaignStats)\n  ]);\n\n  const response = {\n    success: true,\n    timestamp: new Date().toISOString(),\n    data: {\n      stats: {\n        totalEligible: campaignStats[0]?.TotalPacientesElegiveis || 0,\n        lastWeek: campaignStats[0]?.PacientesUltimaSemana || 0,\n        lastMonth: campaignStats[0]?.PacientesUltimoMes || 0\n      },\n      // Mock campaign history for now\n      recentCampaigns: [\n        {\n          id: 1,\n          name: \"Pesquisa de Satisfação - Dezembro\",\n          status: \"completed\",\n          recipients: 245,\n          responses: 89,\n          responseRate: 36.3,\n          cost: 12.25,\n          createdAt: \"2024-12-15T10:00:00Z\"\n        },\n        {\n          id: 2,\n          name: \"Lembrete de Retorno\",\n          status: \"active\",\n          recipients: 156,\n          responses: 23,\n          responseRate: 14.7,\n          cost: 7.80,\n          createdAt: \"2024-12-20T14:30:00Z\"\n        },\n        {\n          id: 3,\n          name: \"Promoção Exames Preventivos\",\n          status: \"scheduled\",\n          recipients: 320,\n          responses: 0,\n          responseRate: 0,\n          cost: 16.00,\n          createdAt: \"2024-12-22T09:00:00Z\"\n        }\n      ]\n    }\n  };\n\n  return NextResponse.json(response);\n}\n\nasync function getAvailableCities() {\n  const cities = await executeQuery(CAMPAIGN_QUERIES.availableCities);\n\n  const response = {\n    success: true,\n    timestamp: new Date().toISOString(),\n    data: {\n      cities: cities.map(row => ({\n        name: row.Cidade,\n        patientCount: parseInt(row.QtdePacientes)\n      }))\n    }\n  };\n\n  return NextResponse.json(response);\n}\n\nasync function getEligiblePatients(searchParams: URLSearchParams) {\n  // Get filter parameters\n  const city = searchParams.get('city');\n  const minAge = searchParams.get('minAge');\n  const maxAge = searchParams.get('maxAge');\n  const lastVisitDays = searchParams.get('lastVisitDays') || '30';\n  const search = searchParams.get('search');\n  const limit = parseInt(searchParams.get('limit') || '100');\n\n  // Build dynamic query with filters\n  let query = CAMPAIGN_QUERIES.eligiblePatients;\n  const conditions = [];\n\n  if (city && city !== 'all') {\n    conditions.push(`PF.EnderecoCidade = '${city}'`);\n  }\n\n  if (minAge) {\n    // Simplified age filter - using birth year approximation\n    const currentYear = new Date().getFullYear();\n    const maxBirthYear = currentYear - parseInt(minAge);\n    conditions.push(`YEAR(PF.DataNascimento) <= ${maxBirthYear}`);\n  }\n\n  if (maxAge) {\n    // Simplified age filter - using birth year approximation\n    const currentYear = new Date().getFullYear();\n    const minBirthYear = currentYear - parseInt(maxAge);\n    conditions.push(`YEAR(PF.DataNascimento) >= ${minBirthYear}`);\n  }\n\n  if (lastVisitDays) {\n    // Use fixed date instead of DATEADD\n    const daysAgo = parseInt(lastVisitDays);\n    const targetDate = new Date();\n    targetDate.setDate(targetDate.getDate() - daysAgo);\n    const dateStr = targetDate.toISOString().split('T')[0];\n    conditions.push(`OS.Data >= '${dateStr}'`);\n  }\n\n  if (search) {\n    conditions.push(`(PF.Nome LIKE '%${search}%' OR PF.TelefoneNumero LIKE '%${search}%')`);\n  }\n\n  if (conditions.length > 0) {\n    query += ' AND ' + conditions.join(' AND ');\n  }\n\n  query += ` ORDER BY OS.Data DESC`;\n\n  const patients = await executeQuery(query);\n\n  // Process patients and format WhatsApp numbers\n  const processedPatients = patients.map(row => {\n    const telefoneCompleto = row.TelefoneDdd && row.TelefoneNumero\n      ? `${row.TelefoneDdd}${row.TelefoneNumero}`\n      : row.TelefoneNumero;\n\n    const whatsappNumber = formatWhatsAppNumber(telefoneCompleto);\n    const age = row.DataNascimento \n      ? new Date().getFullYear() - new Date(row.DataNascimento).getFullYear()\n      : null;\n\n    return {\n      id: row.ID,\n      codigoOs: row.CodigoOs,\n      data: row.Data,\n      pacienteId: row.Paciente,\n      nome: row.Nome,\n      email: row.Email?.trim().toLowerCase(),\n      telefone: telefoneCompleto?.trim(),\n      whatsapp: whatsappNumber,\n      cidade: row.EnderecoCidade,\n      idade: age,\n      totalVisitas: 1 // Simplified for now\n    };\n  }).filter(patient => patient.whatsapp); // Only include patients with valid WhatsApp\n\n  const response = {\n    success: true,\n    timestamp: new Date().toISOString(),\n    data: {\n      patients: processedPatients,\n      total: processedPatients.length,\n      filters: {\n        city,\n        minAge,\n        maxAge,\n        lastVisitDays,\n        search,\n        limit\n      }\n    }\n  };\n\n  return NextResponse.json(response);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,yCAAyC;AACzC,MAAM,mBAAmB;IACvB,mDAAmD;IACnD,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;EAoBnB,CAAC;IAED,sBAAsB;IACtB,eAAe,CAAC;;;;;;;;;;EAUhB,CAAC;IAED,uBAAuB;IACvB,iBAAiB,CAAC;;;;;;;;;;;;EAYlB,CAAC;AACH;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAE7C,IAAI,WAAW,YAAY;YACzB,OAAO,MAAM,oBAAoB;QACnC,OAAO,IAAI,WAAW,UAAU;YAC9B,OAAO,MAAM;QACf,OAAO;YACL,OAAO,MAAM;QACf;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,eAAe;IACb,MAAM,CAAC,cAAc,GAAG,MAAM,QAAQ,GAAG,CAAC;QACxC,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB,aAAa;KAC5C;IAED,MAAM,WAAW;QACf,SAAS;QACT,WAAW,IAAI,OAAO,WAAW;QACjC,MAAM;YACJ,OAAO;gBACL,eAAe,aAAa,CAAC,EAAE,EAAE,2BAA2B;gBAC5D,UAAU,aAAa,CAAC,EAAE,EAAE,yBAAyB;gBACrD,WAAW,aAAa,CAAC,EAAE,EAAE,sBAAsB;YACrD;YACA,gCAAgC;YAChC,iBAAiB;gBACf;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,YAAY;oBACZ,WAAW;oBACX,cAAc;oBACd,MAAM;oBACN,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,YAAY;oBACZ,WAAW;oBACX,cAAc;oBACd,MAAM;oBACN,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,QAAQ;oBACR,YAAY;oBACZ,WAAW;oBACX,cAAc;oBACd,MAAM;oBACN,WAAW;gBACb;aACD;QACH;IACF;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;AAC3B;AAEA,eAAe;IACb,MAAM,SAAS,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB,eAAe;IAElE,MAAM,WAAW;QACf,SAAS;QACT,WAAW,IAAI,OAAO,WAAW;QACjC,MAAM;YACJ,QAAQ,OAAO,GAAG,CAAC,CAAA,MAAO,CAAC;oBACzB,MAAM,IAAI,MAAM;oBAChB,cAAc,SAAS,IAAI,aAAa;gBAC1C,CAAC;QACH;IACF;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;AAC3B;AAEA,eAAe,oBAAoB,YAA6B;IAC9D,wBAAwB;IACxB,MAAM,OAAO,aAAa,GAAG,CAAC;IAC9B,MAAM,SAAS,aAAa,GAAG,CAAC;IAChC,MAAM,SAAS,aAAa,GAAG,CAAC;IAChC,MAAM,gBAAgB,aAAa,GAAG,CAAC,oBAAoB;IAC3D,MAAM,SAAS,aAAa,GAAG,CAAC;IAChC,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;IAEpD,mCAAmC;IACnC,IAAI,QAAQ,iBAAiB,gBAAgB;IAC7C,MAAM,aAAa,EAAE;IAErB,IAAI,QAAQ,SAAS,OAAO;QAC1B,WAAW,IAAI,CAAC,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;IACjD;IAEA,IAAI,QAAQ;QACV,yDAAyD;QACzD,MAAM,cAAc,IAAI,OAAO,WAAW;QAC1C,MAAM,eAAe,cAAc,SAAS;QAC5C,WAAW,IAAI,CAAC,CAAC,2BAA2B,EAAE,cAAc;IAC9D;IAEA,IAAI,QAAQ;QACV,yDAAyD;QACzD,MAAM,cAAc,IAAI,OAAO,WAAW;QAC1C,MAAM,eAAe,cAAc,SAAS;QAC5C,WAAW,IAAI,CAAC,CAAC,2BAA2B,EAAE,cAAc;IAC9D;IAEA,wCAAmB;QACjB,oCAAoC;QACpC,MAAM,UAAU,SAAS;QACzB,MAAM,aAAa,IAAI;QACvB,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;QAC1C,MAAM,UAAU,WAAW,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACtD,WAAW,IAAI,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IAC3C;IAEA,IAAI,QAAQ;QACV,WAAW,IAAI,CAAC,CAAC,gBAAgB,EAAE,OAAO,+BAA+B,EAAE,OAAO,GAAG,CAAC;IACxF;IAEA,IAAI,WAAW,MAAM,GAAG,GAAG;QACzB,SAAS,UAAU,WAAW,IAAI,CAAC;IACrC;IAEA,SAAS,CAAC,sBAAsB,CAAC;IAEjC,MAAM,WAAW,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE;IAEpC,+CAA+C;IAC/C,MAAM,oBAAoB,SAAS,GAAG,CAAC,CAAA;QACrC,MAAM,mBAAmB,IAAI,WAAW,IAAI,IAAI,cAAc,GAC1D,GAAG,IAAI,WAAW,GAAG,IAAI,cAAc,EAAE,GACzC,IAAI,cAAc;QAEtB,MAAM,iBAAiB,CAAA,GAAA,wHAAA,CAAA,uBAAoB,AAAD,EAAE;QAC5C,MAAM,MAAM,IAAI,cAAc,GAC1B,IAAI,OAAO,WAAW,KAAK,IAAI,KAAK,IAAI,cAAc,EAAE,WAAW,KACnE;QAEJ,OAAO;YACL,IAAI,IAAI,EAAE;YACV,UAAU,IAAI,QAAQ;YACtB,MAAM,IAAI,IAAI;YACd,YAAY,IAAI,QAAQ;YACxB,MAAM,IAAI,IAAI;YACd,OAAO,IAAI,KAAK,EAAE,OAAO;YACzB,UAAU,kBAAkB;YAC5B,UAAU;YACV,QAAQ,IAAI,cAAc;YAC1B,OAAO;YACP,cAAc,EAAE,qBAAqB;QACvC;IACF,GAAG,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,GAAG,4CAA4C;IAEpF,MAAM,WAAW;QACf,SAAS;QACT,WAAW,IAAI,OAAO,WAAW;QACjC,MAAM;YACJ,UAAU;YACV,OAAO,kBAAkB,MAAM;YAC/B,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF;IACF;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;AAC3B", "debugId": null}}]}