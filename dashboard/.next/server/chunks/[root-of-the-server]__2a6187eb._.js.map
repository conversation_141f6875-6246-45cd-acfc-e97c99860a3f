{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/lib/database.ts"], "sourcesContent": ["import * as odbc from 'odbc';\n\n// Database configuration\nconst dbConfig = {\n  host: process.env.IRIS_HOST || 'localhost',\n  port: parseInt(process.env.IRIS_PORT || '1972'),\n  namespace: process.env.IRIS_NAMESPACE || 'dado',\n  user: process.env.IRIS_USER || '_SYSTEM',\n  password: process.env.IRIS_PASSWORD || 'SYS',\n  sslMode: parseInt(process.env.IRIS_SSL_MODE || '0'),\n};\n\n// Build connection string\nfunction buildConnectionString() {\n  const driverPath = process.env.IRIS_DRIVER_PATH;\n  \n  // If no driver path is specified or is \"system\", use system driver name\n  const driverPart = (!driverPath || driverPath === 'system') \n    ? 'DRIVER={InterSystems ODBC35}'\n    : `DRIVER=${driverPath}`;\n\n  return `${driverPart};` +\n         `SERVER=${dbConfig.host};` +\n         `PORT=${dbConfig.port};` +\n         `DATABASE=${dbConfig.namespace};` +\n         `UID=${dbConfig.user};` +\n         `PWD=${dbConfig.password};` +\n         `SSL_MODE=${dbConfig.sslMode};`;\n}\n\n// Get database connection\nexport async function getConnection() {\n  try {\n    const connectionString = buildConnectionString();\n    \n    const connection = await odbc.connect({\n      connectionString,\n      connectionTimeout: 30,\n      loginTimeout: 30,\n    });\n    \n    console.log('Database connection established');\n    return connection;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    throw error;\n  }\n}\n\n// Close database connection\nexport async function closeConnection(connection: any) {\n  if (connection) {\n    try {\n      await connection.close();\n      console.log('Database connection closed');\n    } catch (error) {\n      console.error('Error closing connection:', error);\n    }\n  }\n}\n\n// Execute query with connection management\nexport async function executeQuery<T = any>(query: string, params: any[] = []): Promise<T[]> {\n  let connection = null;\n  \n  try {\n    connection = await getConnection();\n    const results = await connection.query(query, params);\n    \n    // Convert BigInt to string for JSON serialization\n    const processedResults = results.map((row: any) => {\n      const processedRow: any = {};\n      for (const [key, value] of Object.entries(row)) {\n        if (typeof value === 'bigint') {\n          processedRow[key] = value.toString();\n        } else {\n          processedRow[key] = value;\n        }\n      }\n      return processedRow;\n    });\n    \n    return processedResults;\n  } catch (error) {\n    console.error('Query execution failed:', error);\n    throw error;\n  } finally {\n    await closeConnection(connection);\n  }\n}\n\n// Utility function to format WhatsApp number\nexport function formatWhatsAppNumber(telefone: string | null): string | null {\n  if (!telefone) return null;\n\n  // Clean number (remove special characters)\n  const cleanNumber = telefone.replace(/\\D/g, '');\n\n  // Check if it has at least 10 digits (Brazilian format)\n  if (cleanNumber.length < 10) return null;\n\n  // Add country code if it doesn't have one (55 for Brazil)\n  let formattedNumber = cleanNumber;\n  if (!formattedNumber.startsWith('55') && formattedNumber.length <= 11) {\n    formattedNumber = '55' + formattedNumber;\n  }\n\n  // Check if it's a valid number (12 or 13 digits with country code)\n  if (formattedNumber.length === 13 || formattedNumber.length === 12) {\n    return formattedNumber;\n  }\n\n  return null;\n}\n\n// Utility function to build date range filter\nexport function buildDateRangeFilter(startDate?: string, endDate?: string): string {\n  if (!startDate && !endDate) return '';\n  \n  if (startDate && endDate) {\n    return `WHERE OS.Data BETWEEN '${startDate}' AND '${endDate}'`;\n  } else if (startDate) {\n    return `WHERE OS.Data >= '${startDate}'`;\n  } else if (endDate) {\n    return `WHERE OS.Data <= '${endDate}'`;\n  }\n  \n  return '';\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA,yBAAyB;AACzB,MAAM,WAAW;IACf,MAAM,QAAQ,GAAG,CAAC,SAAS,IAAI;IAC/B,MAAM,SAAS,QAAQ,GAAG,CAAC,SAAS,IAAI;IACxC,WAAW,QAAQ,GAAG,CAAC,cAAc,IAAI;IACzC,MAAM,QAAQ,GAAG,CAAC,SAAS,IAAI;IAC/B,UAAU,QAAQ,GAAG,CAAC,aAAa,IAAI;IACvC,SAAS,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;AACjD;AAEA,0BAA0B;AAC1B,SAAS;IACP,MAAM,aAAa,QAAQ,GAAG,CAAC,gBAAgB;IAE/C,wEAAwE;IACxE,MAAM,aAAa,AAAC,CAAC,cAAc,eAAe,WAC9C,iCACA,CAAC,OAAO,EAAE,YAAY;IAE1B,OAAO,GAAG,WAAW,CAAC,CAAC,GAChB,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,GAC1B,CAAC,KAAK,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,GACxB,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC,CAAC,CAAC,GACjC,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,GACvB,CAAC,IAAI,EAAE,SAAS,QAAQ,CAAC,CAAC,CAAC,GAC3B,CAAC,SAAS,EAAE,SAAS,OAAO,CAAC,CAAC,CAAC;AACxC;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,mBAAmB;QAEzB,MAAM,aAAa,MAAM,CAAA,GAAA,iGAAA,CAAA,UAAY,AAAD,EAAE;YACpC;YACA,mBAAmB;YACnB,cAAc;QAChB;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAGO,eAAe,gBAAgB,UAAe;IACnD,IAAI,YAAY;QACd,IAAI;YACF,MAAM,WAAW,KAAK;YACtB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;AACF;AAGO,eAAe,aAAsB,KAAa,EAAE,SAAgB,EAAE;IAC3E,IAAI,aAAa;IAEjB,IAAI;QACF,aAAa,MAAM;QACnB,MAAM,UAAU,MAAM,WAAW,KAAK,CAAC,OAAO;QAE9C,kDAAkD;QAClD,MAAM,mBAAmB,QAAQ,GAAG,CAAC,CAAC;YACpC,MAAM,eAAoB,CAAC;YAC3B,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAM;gBAC9C,IAAI,OAAO,UAAU,UAAU;oBAC7B,YAAY,CAAC,IAAI,GAAG,MAAM,QAAQ;gBACpC,OAAO;oBACL,YAAY,CAAC,IAAI,GAAG;gBACtB;YACF;YACA,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR,SAAU;QACR,MAAM,gBAAgB;IACxB;AACF;AAGO,SAAS,qBAAqB,QAAuB;IAC1D,IAAI,CAAC,UAAU,OAAO;IAEtB,2CAA2C;IAC3C,MAAM,cAAc,SAAS,OAAO,CAAC,OAAO;IAE5C,wDAAwD;IACxD,IAAI,YAAY,MAAM,GAAG,IAAI,OAAO;IAEpC,0DAA0D;IAC1D,IAAI,kBAAkB;IACtB,IAAI,CAAC,gBAAgB,UAAU,CAAC,SAAS,gBAAgB,MAAM,IAAI,IAAI;QACrE,kBAAkB,OAAO;IAC3B;IAEA,mEAAmE;IACnE,IAAI,gBAAgB,MAAM,KAAK,MAAM,gBAAgB,MAAM,KAAK,IAAI;QAClE,OAAO;IACT;IAEA,OAAO;AACT;AAGO,SAAS,qBAAqB,SAAkB,EAAE,OAAgB;IACvE,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;IAEnC,IAAI,aAAa,SAAS;QACxB,OAAO,CAAC,uBAAuB,EAAE,UAAU,OAAO,EAAE,QAAQ,CAAC,CAAC;IAChE,OAAO,IAAI,WAAW;QACpB,OAAO,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;IAC1C,OAAO,IAAI,SAAS;QAClB,OAAO,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;IACxC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/app/api/services/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { executeQuery } from '@/lib/database';\n\n// Service analytics queries\nconst SERVICE_QUERIES = {\n  // Total service orders\n  totalOrders: `\n    SELECT COUNT(*) AS TotalOS\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)\n  `,\n\n  // Average service time\n  averageServiceTime: `\n    SELECT\n      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS TempoMedioAtendimentoMin\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.HoraInicial IS NOT NULL \n      AND OS.HoraFinal IS NOT NULL\n      AND OS.Data >= DATEADD('day', -30, CURRENT_DATE)\n  `,\n\n  // Service volume trend (last 30 days)\n  volumeTrend: `\n    SELECT\n      OS.Data,\n      COUNT(*) AS QtdeOS\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)\n    GROUP BY OS.Data\n    ORDER BY OS.Data\n  `,\n\n  // Service distribution by type\n  serviceTypes: `\n    SELECT\n      OS.TipoAtendimento,\n      COUNT(*) AS QtdeOS,\n      COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() AS PercOS\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)\n      AND OS.TipoAtendimento IS NOT NULL\n    GROUP BY OS.TipoAtendimento\n    ORDER BY QtdeOS DESC\n  `,\n\n  // Service time distribution by hour\n  timeDistribution: `\n    SELECT\n      DATEPART('hour', OS.HoraInicial) AS Hora,\n      COUNT(*) AS QtdeOS\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.HoraInicial IS NOT NULL\n      AND OS.Data >= DATEADD('day', -30, CURRENT_DATE)\n    GROUP BY DATEPART('hour', OS.HoraInicial)\n    ORDER BY Hora\n  `,\n\n  // Weekly patterns\n  weeklyPattern: `\n    SELECT\n      DATEPART('weekday', OS.Data) AS DiaSemana,\n      COUNT(*) AS QtdeOS\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)\n    GROUP BY DATEPART('weekday', OS.Data)\n    ORDER BY DiaSemana\n  `,\n\n  // Online vs Presential\n  onlineVsPresential: `\n    SELECT\n      CASE \n        WHEN OS.DisponivelWeb = 1 THEN 'Online'\n        ELSE 'Presencial'\n      END AS TipoAgendamento,\n      COUNT(*) AS QtdeOS,\n      COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() AS PercOS\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)\n    GROUP BY OS.DisponivelWeb\n  `,\n\n  // Staff performance\n  staffPerformance: `\n    SELECT\n      OS.Recepcionista AS ID_Recepcionista,\n      COUNT(*) AS QtdeOS,\n      AVG(DATEDIFF('minute', OS.HoraInicial, OS.HoraFinal)) AS TempoMedioMin\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= DATEADD('day', -30, CURRENT_DATE)\n      AND OS.Recepcionista IS NOT NULL\n      AND OS.HoraInicial IS NOT NULL \n      AND OS.HoraFinal IS NOT NULL\n    GROUP BY OS.Recepcionista\n    ORDER BY QtdeOS DESC\n    FETCH FIRST 10 ROWS ONLY\n  `,\n\n  // Reprint rate\n  reprintRate: `\n    SELECT\n      COUNT(*) * 100.0 / (\n        SELECT COUNT(*) \n        FROM dado.ArqOrdemServico \n        WHERE Data >= DATEADD('day', -30, CURRENT_DATE)\n      ) AS TaxaReimpressaoPerc\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.ReimpressaoData IS NOT NULL\n      AND OS.Data >= DATEADD('day', -30, CURRENT_DATE)\n  `\n};\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const startDate = searchParams.get('startDate');\n    const endDate = searchParams.get('endDate');\n\n    // Execute all service analytics queries\n    const [\n      totalOrders,\n      averageServiceTime,\n      volumeTrend,\n      serviceTypes,\n      timeDistribution,\n      weeklyPattern,\n      onlineVsPresential,\n      staffPerformance,\n      reprintRate\n    ] = await Promise.all([\n      executeQuery(SERVICE_QUERIES.totalOrders),\n      executeQuery(SERVICE_QUERIES.averageServiceTime),\n      executeQuery(SERVICE_QUERIES.volumeTrend),\n      executeQuery(SERVICE_QUERIES.serviceTypes),\n      executeQuery(SERVICE_QUERIES.timeDistribution),\n      executeQuery(SERVICE_QUERIES.weeklyPattern),\n      executeQuery(SERVICE_QUERIES.onlineVsPresential),\n      executeQuery(SERVICE_QUERIES.staffPerformance),\n      executeQuery(SERVICE_QUERIES.reprintRate)\n    ]);\n\n    // Format the response\n    const response = {\n      success: true,\n      timestamp: new Date().toISOString(),\n      data: {\n        metrics: {\n          totalOrders: totalOrders[0]?.TotalOS || 0,\n          averageServiceTime: Math.round(averageServiceTime[0]?.TempoMedioAtendimentoMin || 0),\n          reprintRate: parseFloat((reprintRate[0]?.TaxaReimpressaoPerc || 0).toFixed(2))\n        },\n        trends: {\n          volume: volumeTrend.map(row => ({\n            date: row.Data,\n            orders: parseInt(row.QtdeOS)\n          })),\n          timeDistribution: timeDistribution.map(row => ({\n            hour: parseInt(row.Hora),\n            orders: parseInt(row.QtdeOS)\n          })),\n          weeklyPattern: weeklyPattern.map(row => ({\n            dayOfWeek: parseInt(row.DiaSemana),\n            orders: parseInt(row.QtdeOS)\n          }))\n        },\n        distribution: {\n          serviceTypes: serviceTypes.map(row => ({\n            type: row.TipoAtendimento,\n            count: parseInt(row.QtdeOS),\n            percentage: parseFloat(row.PercOS.toFixed(2))\n          })),\n          onlineVsPresential: onlineVsPresential.map(row => ({\n            type: row.TipoAgendamento,\n            count: parseInt(row.QtdeOS),\n            percentage: parseFloat(row.PercOS.toFixed(2))\n          }))\n        },\n        staff: staffPerformance.map(row => ({\n          id: row.ID_Recepcionista,\n          orders: parseInt(row.QtdeOS),\n          averageTime: Math.round(row.TempoMedioMin || 0)\n        }))\n      }\n    };\n\n    return NextResponse.json(response);\n\n  } catch (error) {\n    console.error('Error fetching service analytics:', error);\n    return NextResponse.json(\n      {\n        success: false,\n        error: 'Failed to fetch service analytics',\n        message: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,4BAA4B;AAC5B,MAAM,kBAAkB;IACtB,uBAAuB;IACvB,aAAa,CAAC;;;;EAId,CAAC;IAED,uBAAuB;IACvB,oBAAoB,CAAC;;;;;;;EAOrB,CAAC;IAED,sCAAsC;IACtC,aAAa,CAAC;;;;;;;;EAQd,CAAC;IAED,+BAA+B;IAC/B,cAAc,CAAC;;;;;;;;;;EAUf,CAAC;IAED,oCAAoC;IACpC,kBAAkB,CAAC;;;;;;;;;EASnB,CAAC;IAED,kBAAkB;IAClB,eAAe,CAAC;;;;;;;;EAQhB,CAAC;IAED,uBAAuB;IACvB,oBAAoB,CAAC;;;;;;;;;;;EAWrB,CAAC;IAED,oBAAoB;IACpB,kBAAkB,CAAC;;;;;;;;;;;;;EAanB,CAAC;IAED,eAAe;IACf,aAAa,CAAC;;;;;;;;;;EAUd,CAAC;AACH;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,UAAU,aAAa,GAAG,CAAC;QAEjC,wCAAwC;QACxC,MAAM,CACJ,aACA,oBACA,aACA,cACA,kBACA,eACA,oBACA,kBACA,YACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,WAAW;YACxC,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,kBAAkB;YAC/C,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,WAAW;YACxC,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,YAAY;YACzC,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,gBAAgB;YAC7C,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,aAAa;YAC1C,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,kBAAkB;YAC/C,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,gBAAgB;YAC7C,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,WAAW;SACzC;QAED,sBAAsB;QACtB,MAAM,WAAW;YACf,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;YACjC,MAAM;gBACJ,SAAS;oBACP,aAAa,WAAW,CAAC,EAAE,EAAE,WAAW;oBACxC,oBAAoB,KAAK,KAAK,CAAC,kBAAkB,CAAC,EAAE,EAAE,4BAA4B;oBAClF,aAAa,WAAW,CAAC,WAAW,CAAC,EAAE,EAAE,uBAAuB,CAAC,EAAE,OAAO,CAAC;gBAC7E;gBACA,QAAQ;oBACN,QAAQ,YAAY,GAAG,CAAC,CAAA,MAAO,CAAC;4BAC9B,MAAM,IAAI,IAAI;4BACd,QAAQ,SAAS,IAAI,MAAM;wBAC7B,CAAC;oBACD,kBAAkB,iBAAiB,GAAG,CAAC,CAAA,MAAO,CAAC;4BAC7C,MAAM,SAAS,IAAI,IAAI;4BACvB,QAAQ,SAAS,IAAI,MAAM;wBAC7B,CAAC;oBACD,eAAe,cAAc,GAAG,CAAC,CAAA,MAAO,CAAC;4BACvC,WAAW,SAAS,IAAI,SAAS;4BACjC,QAAQ,SAAS,IAAI,MAAM;wBAC7B,CAAC;gBACH;gBACA,cAAc;oBACZ,cAAc,aAAa,GAAG,CAAC,CAAA,MAAO,CAAC;4BACrC,MAAM,IAAI,eAAe;4BACzB,OAAO,SAAS,IAAI,MAAM;4BAC1B,YAAY,WAAW,IAAI,MAAM,CAAC,OAAO,CAAC;wBAC5C,CAAC;oBACD,oBAAoB,mBAAmB,GAAG,CAAC,CAAA,MAAO,CAAC;4BACjD,MAAM,IAAI,eAAe;4BACzB,OAAO,SAAS,IAAI,MAAM;4BAC1B,YAAY,WAAW,IAAI,MAAM,CAAC,OAAO,CAAC;wBAC5C,CAAC;gBACH;gBACA,OAAO,iBAAiB,GAAG,CAAC,CAAA,MAAO,CAAC;wBAClC,IAAI,IAAI,gBAAgB;wBACxB,QAAQ,SAAS,IAAI,MAAM;wBAC3B,aAAa,KAAK,KAAK,CAAC,IAAI,aAAa,IAAI;oBAC/C,CAAC;YACH;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}