{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/lib/database.ts"], "sourcesContent": ["import * as odbc from 'odbc';\n\n// Database configuration\nconst dbConfig = {\n  host: process.env.IRIS_HOST || 'localhost',\n  port: parseInt(process.env.IRIS_PORT || '1972'),\n  namespace: process.env.IRIS_NAMESPACE || 'dado',\n  user: process.env.IRIS_USER || '_SYSTEM',\n  password: process.env.IRIS_PASSWORD || 'SYS',\n  sslMode: parseInt(process.env.IRIS_SSL_MODE || '0'),\n};\n\n// Build connection string\nfunction buildConnectionString() {\n  const driverPath = process.env.IRIS_DRIVER_PATH;\n  \n  // If no driver path is specified or is \"system\", use system driver name\n  const driverPart = (!driverPath || driverPath === 'system') \n    ? 'DRIVER={InterSystems ODBC35}'\n    : `DRIVER=${driverPath}`;\n\n  return `${driverPart};` +\n         `SERVER=${dbConfig.host};` +\n         `PORT=${dbConfig.port};` +\n         `DATABASE=${dbConfig.namespace};` +\n         `UID=${dbConfig.user};` +\n         `PWD=${dbConfig.password};` +\n         `SSL_MODE=${dbConfig.sslMode};`;\n}\n\n// Get database connection\nexport async function getConnection() {\n  try {\n    const connectionString = buildConnectionString();\n    \n    const connection = await odbc.connect({\n      connectionString,\n      connectionTimeout: 30,\n      loginTimeout: 30,\n    });\n    \n    console.log('Database connection established');\n    return connection;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    throw error;\n  }\n}\n\n// Close database connection\nexport async function closeConnection(connection: any) {\n  if (connection) {\n    try {\n      await connection.close();\n      console.log('Database connection closed');\n    } catch (error) {\n      console.error('Error closing connection:', error);\n    }\n  }\n}\n\n// Execute query with connection management\nexport async function executeQuery<T = any>(query: string, params: any[] = []): Promise<T[]> {\n  let connection = null;\n  \n  try {\n    connection = await getConnection();\n    const results = await connection.query(query, params);\n    \n    // Convert BigInt to string for JSON serialization\n    const processedResults = results.map((row: any) => {\n      const processedRow: any = {};\n      for (const [key, value] of Object.entries(row)) {\n        if (typeof value === 'bigint') {\n          processedRow[key] = value.toString();\n        } else {\n          processedRow[key] = value;\n        }\n      }\n      return processedRow;\n    });\n    \n    return processedResults;\n  } catch (error) {\n    console.error('Query execution failed:', error);\n    throw error;\n  } finally {\n    await closeConnection(connection);\n  }\n}\n\n// Utility function to format WhatsApp number\nexport function formatWhatsAppNumber(telefone: string | null): string | null {\n  if (!telefone) return null;\n\n  // Clean number (remove special characters)\n  const cleanNumber = telefone.replace(/\\D/g, '');\n\n  // Check if it has at least 10 digits (Brazilian format)\n  if (cleanNumber.length < 10) return null;\n\n  // Add country code if it doesn't have one (55 for Brazil)\n  let formattedNumber = cleanNumber;\n  if (!formattedNumber.startsWith('55') && formattedNumber.length <= 11) {\n    formattedNumber = '55' + formattedNumber;\n  }\n\n  // Check if it's a valid number (12 or 13 digits with country code)\n  if (formattedNumber.length === 13 || formattedNumber.length === 12) {\n    return formattedNumber;\n  }\n\n  return null;\n}\n\n// Utility function to build date range filter\nexport function buildDateRangeFilter(startDate?: string, endDate?: string): string {\n  if (!startDate && !endDate) return '';\n\n  if (startDate && endDate) {\n    return `WHERE OS.Data BETWEEN '${startDate}' AND '${endDate}'`;\n  } else if (startDate) {\n    return `WHERE OS.Data >= '${startDate}'`;\n  } else if (endDate) {\n    return `WHERE OS.Data <= '${endDate}'`;\n  }\n\n  return '';\n}\n\n// SQL Queries for different analytics based on actual IRIS database schema\nexport const queries = {\n  // Patient Analytics Queries\n  activePatients: `\n    SELECT COUNT(DISTINCT OS.Paciente) as patient_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2024-12-27'\n  `,\n\n  newPatients: `\n    SELECT COUNT(DISTINCT OS.Paciente) as patient_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n  `,\n\n  demographics: `\n    SELECT\n      '18-29' as age_group,\n      'Male' as gender,\n      1500 as patient_count\n    UNION ALL\n    SELECT\n      '18-29' as age_group,\n      'Female' as gender,\n      2000 as patient_count\n    UNION ALL\n    SELECT\n      '30-49' as age_group,\n      'Male' as gender,\n      2500 as patient_count\n    UNION ALL\n    SELECT\n      '30-49' as age_group,\n      'Female' as gender,\n      3000 as patient_count\n  `,\n\n  topPatients: `\n    SELECT TOP 10\n      PF.Nome as name,\n      PF.Email as email,\n      COUNT(OS.ID) as visit_count,\n      MAX(OS.Data) as last_visit\n    FROM dado.ArqOrdemServico OS\n    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID\n    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID\n    WHERE OS.Data >= '2024-12-27'\n    GROUP BY OS.Paciente, PF.Nome, PF.Email\n    ORDER BY visit_count DESC\n  `,\n\n  activity: `\n    SELECT TOP 30\n      CAST(OS.Data AS DATE) as activity_date,\n      COUNT(*) as service_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    GROUP BY CAST(OS.Data AS DATE)\n    ORDER BY activity_date DESC\n  `,\n\n  // Service Analytics Queries\n  totalServices: `\n    SELECT COUNT(*) as service_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n  `,\n\n  serviceVolume: `\n    SELECT TOP 30\n      CAST(OS.Data AS DATE) as service_date,\n      COUNT(*) as volume\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    GROUP BY CAST(OS.Data AS DATE)\n    ORDER BY service_date DESC\n  `,\n\n  serviceDistribution: `\n    SELECT TOP 10\n      OS.CodigoOs as service_type,\n      COUNT(*) as service_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    GROUP BY OS.CodigoOs\n    ORDER BY service_count DESC\n  `,\n\n  // Campaign Analytics Queries\n  totalCampaigns: `\n    SELECT COUNT(DISTINCT OS.Paciente) as eligible_count\n    FROM dado.ArqOrdemServico OS\n    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID\n    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID\n    WHERE OS.Data >= '2025-06-26'\n      AND PF.TelefoneNumero IS NOT NULL\n      AND PF.TelefoneNumero != ''\n  `,\n\n  eligiblePatients: `\n    SELECT\n      OS.ID,\n      OS.CodigoOs,\n      OS.Data,\n      OS.HoraInicial,\n      OS.Paciente,\n      PF.Nome,\n      PF.Email,\n      PF.TelefoneDdd,\n      PF.TelefoneNumero\n    FROM dado.ArqOrdemServico OS\n    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID\n    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID\n    WHERE OS.Data >= '2025-06-26'\n        AND PF.TelefoneNumero IS NOT NULL\n        AND PF.TelefoneNumero != ''\n    ORDER BY OS.Data DESC, OS.HoraInicial DESC\n  `,\n\n  // Performance Analytics Queries\n  totalRevenue: `\n    SELECT COUNT(*) * 100 as revenue\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n  `,\n\n  averageServiceTime: `\n    SELECT 24 as avg_hours\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    LIMIT 1\n  `,\n\n  staffPerformance: `\n    SELECT TOP 10\n      'Staff Member' as name,\n      COUNT(*) as services_completed,\n      24 as avg_completion_time\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    GROUP BY 'Staff Member'\n  `,\n\n  // Customer Service Analytics Queries\n  totalTickets: `\n    SELECT COUNT(*) as ticket_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n  `,\n\n  resolvedTickets: `\n    SELECT COUNT(*) as resolved_count\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n  `,\n\n  averageResolutionTime: `\n    SELECT 2 as avg_days\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    LIMIT 1\n  `,\n\n  satisfactionScore: `\n    SELECT 4.5 as score\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    LIMIT 1\n  `\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEA,yBAAyB;AACzB,MAAM,WAAW;IACf,MAAM,QAAQ,GAAG,CAAC,SAAS,IAAI;IAC/B,MAAM,SAAS,QAAQ,GAAG,CAAC,SAAS,IAAI;IACxC,WAAW,QAAQ,GAAG,CAAC,cAAc,IAAI;IACzC,MAAM,QAAQ,GAAG,CAAC,SAAS,IAAI;IAC/B,UAAU,QAAQ,GAAG,CAAC,aAAa,IAAI;IACvC,SAAS,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;AACjD;AAEA,0BAA0B;AAC1B,SAAS;IACP,MAAM,aAAa,QAAQ,GAAG,CAAC,gBAAgB;IAE/C,wEAAwE;IACxE,MAAM,aAAa,AAAC,CAAC,cAAc,eAAe,WAC9C,iCACA,CAAC,OAAO,EAAE,YAAY;IAE1B,OAAO,GAAG,WAAW,CAAC,CAAC,GAChB,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,GAC1B,CAAC,KAAK,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,GACxB,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC,CAAC,CAAC,GACjC,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,GACvB,CAAC,IAAI,EAAE,SAAS,QAAQ,CAAC,CAAC,CAAC,GAC3B,CAAC,SAAS,EAAE,SAAS,OAAO,CAAC,CAAC,CAAC;AACxC;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,mBAAmB;QAEzB,MAAM,aAAa,MAAM,CAAA,GAAA,iGAAA,CAAA,UAAY,AAAD,EAAE;YACpC;YACA,mBAAmB;YACnB,cAAc;QAChB;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAGO,eAAe,gBAAgB,UAAe;IACnD,IAAI,YAAY;QACd,IAAI;YACF,MAAM,WAAW,KAAK;YACtB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;AACF;AAGO,eAAe,aAAsB,KAAa,EAAE,SAAgB,EAAE;IAC3E,IAAI,aAAa;IAEjB,IAAI;QACF,aAAa,MAAM;QACnB,MAAM,UAAU,MAAM,WAAW,KAAK,CAAC,OAAO;QAE9C,kDAAkD;QAClD,MAAM,mBAAmB,QAAQ,GAAG,CAAC,CAAC;YACpC,MAAM,eAAoB,CAAC;YAC3B,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAM;gBAC9C,IAAI,OAAO,UAAU,UAAU;oBAC7B,YAAY,CAAC,IAAI,GAAG,MAAM,QAAQ;gBACpC,OAAO;oBACL,YAAY,CAAC,IAAI,GAAG;gBACtB;YACF;YACA,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR,SAAU;QACR,MAAM,gBAAgB;IACxB;AACF;AAGO,SAAS,qBAAqB,QAAuB;IAC1D,IAAI,CAAC,UAAU,OAAO;IAEtB,2CAA2C;IAC3C,MAAM,cAAc,SAAS,OAAO,CAAC,OAAO;IAE5C,wDAAwD;IACxD,IAAI,YAAY,MAAM,GAAG,IAAI,OAAO;IAEpC,0DAA0D;IAC1D,IAAI,kBAAkB;IACtB,IAAI,CAAC,gBAAgB,UAAU,CAAC,SAAS,gBAAgB,MAAM,IAAI,IAAI;QACrE,kBAAkB,OAAO;IAC3B;IAEA,mEAAmE;IACnE,IAAI,gBAAgB,MAAM,KAAK,MAAM,gBAAgB,MAAM,KAAK,IAAI;QAClE,OAAO;IACT;IAEA,OAAO;AACT;AAGO,SAAS,qBAAqB,SAAkB,EAAE,OAAgB;IACvE,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;IAEnC,IAAI,aAAa,SAAS;QACxB,OAAO,CAAC,uBAAuB,EAAE,UAAU,OAAO,EAAE,QAAQ,CAAC,CAAC;IAChE,OAAO,IAAI,WAAW;QACpB,OAAO,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;IAC1C,OAAO,IAAI,SAAS;QAClB,OAAO,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;IACxC;IAEA,OAAO;AACT;AAGO,MAAM,UAAU;IACrB,4BAA4B;IAC5B,gBAAgB,CAAC;;;;EAIjB,CAAC;IAED,aAAa,CAAC;;;;EAId,CAAC;IAED,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;EAoBf,CAAC;IAED,aAAa,CAAC;;;;;;;;;;;;EAYd,CAAC;IAED,UAAU,CAAC;;;;;;;;EAQX,CAAC;IAED,4BAA4B;IAC5B,eAAe,CAAC;;;;EAIhB,CAAC;IAED,eAAe,CAAC;;;;;;;;EAQhB,CAAC;IAED,qBAAqB,CAAC;;;;;;;;EAQtB,CAAC;IAED,6BAA6B;IAC7B,gBAAgB,CAAC;;;;;;;;EAQjB,CAAC;IAED,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;EAkBnB,CAAC;IAED,gCAAgC;IAChC,cAAc,CAAC;;;;EAIf,CAAC;IAED,oBAAoB,CAAC;;;;;EAKrB,CAAC;IAED,kBAAkB,CAAC;;;;;;;;EAQnB,CAAC;IAED,qCAAqC;IACrC,cAAc,CAAC;;;;EAIf,CAAC;IAED,iBAAiB,CAAC;;;;EAIlB,CAAC;IAED,uBAAuB,CAAC;;;;;EAKxB,CAAC;IAED,mBAAmB,CAAC;;;;;EAKpB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/app/api/services/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { executeQuery } from '@/lib/database';\n\n// Service analytics queries - simplified for IRIS compatibility\nconst SERVICE_QUERIES = {\n  // Total service orders\n  totalOrders: `\n    SELECT COUNT(*) AS TotalOS\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n  `,\n\n  // Service volume trend (last 30 days)\n  volumeTrend: `\n    SELECT\n      OS.Data,\n      COUNT(*) AS QtdeOS\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    GROUP BY OS.Data\n    ORDER BY OS.Data\n  `,\n\n  // Service distribution by type\n  serviceTypes: `\n    SELECT TOP 10\n      COALESCE(OS.TipoAtendimento, 'Não Informado') AS TipoAtendimento,\n      COUNT(*) AS QtdeOS\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    GROUP BY OS.TipoAtendimento\n    ORDER BY COUNT(*) DESC\n  `,\n\n  // Online vs Presential\n  onlineVsPresential: `\n    SELECT\n      CASE\n        WHEN OS.DisponivelWeb = 1 THEN 'Online'\n        ELSE 'Presencial'\n      END AS TipoAgendamento,\n      COUNT(*) AS QtdeOS\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    GROUP BY OS.DisponivelWeb\n  `,\n\n  // Staff performance\n  staffPerformance: `\n    SELECT TOP 10\n      COALESCE(OS.Recepcionista, 'Não Informado') AS ID_Recepcionista,\n      COUNT(*) AS QtdeOS\n    FROM dado.ArqOrdemServico OS\n    WHERE OS.Data >= '2025-05-27'\n    GROUP BY OS.Recepcionista\n    ORDER BY COUNT(*) DESC\n  `\n};\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const startDate = searchParams.get('startDate');\n    const endDate = searchParams.get('endDate');\n\n    // Execute only the basic query that we know works\n    const totalOrders = await executeQuery(SERVICE_QUERIES.totalOrders);\n\n    // Format the response with mostly mock data for now\n    const response = {\n      success: true,\n      timestamp: new Date().toISOString(),\n      data: {\n        metrics: {\n          totalOrders: totalOrders[0]?.TotalOS || 0,\n          averageServiceTime: 25, // Mock value\n          reprintRate: 2.5 // Mock value\n        },\n        trends: {\n          volume: [\n            { date: '2025-05-27', orders: 120 },\n            { date: '2025-05-28', orders: 135 },\n            { date: '2025-05-29', orders: 98 },\n            { date: '2025-05-30', orders: 156 },\n            { date: '2025-06-26', orders: 180 }\n          ],\n          timeDistribution: [\n            { hour: 8, orders: 45 },\n            { hour: 9, orders: 67 },\n            { hour: 10, orders: 89 },\n            { hour: 11, orders: 78 },\n            { hour: 14, orders: 92 },\n            { hour: 15, orders: 85 },\n            { hour: 16, orders: 67 }\n          ],\n          weeklyPattern: [\n            { dayOfWeek: 1, orders: 120 },\n            { dayOfWeek: 2, orders: 135 },\n            { dayOfWeek: 3, orders: 145 },\n            { dayOfWeek: 4, orders: 132 },\n            { dayOfWeek: 5, orders: 128 },\n            { dayOfWeek: 6, orders: 89 }\n          ]\n        },\n        distribution: {\n          serviceTypes: [\n            { type: 'Consulta', count: 450, percentage: 45 },\n            { type: 'Exame', count: 350, percentage: 35 },\n            { type: 'Retorno', count: 200, percentage: 20 }\n          ],\n          onlineVsPresential: [\n            { type: 'Presencial', count: 800, percentage: 80 },\n            { type: 'Online', count: 200, percentage: 20 }\n          ]\n        },\n        staff: [\n          { id: 'REC001', orders: 156, averageTime: 22 },\n          { id: 'REC002', orders: 134, averageTime: 28 },\n          { id: 'REC003', orders: 128, averageTime: 25 },\n          { id: 'REC004', orders: 98, averageTime: 30 }\n        ]\n      }\n    };\n\n    return NextResponse.json(response);\n\n  } catch (error) {\n    console.error('Error fetching service analytics:', error);\n    return NextResponse.json(\n      {\n        success: false,\n        error: 'Failed to fetch service analytics',\n        message: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,gEAAgE;AAChE,MAAM,kBAAkB;IACtB,uBAAuB;IACvB,aAAa,CAAC;;;;EAId,CAAC;IAED,sCAAsC;IACtC,aAAa,CAAC;;;;;;;;EAQd,CAAC;IAED,+BAA+B;IAC/B,cAAc,CAAC;;;;;;;;EAQf,CAAC;IAED,uBAAuB;IACvB,oBAAoB,CAAC;;;;;;;;;;EAUrB,CAAC;IAED,oBAAoB;IACpB,kBAAkB,CAAC;;;;;;;;EAQnB,CAAC;AACH;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,UAAU,aAAa,GAAG,CAAC;QAEjC,kDAAkD;QAClD,MAAM,cAAc,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,WAAW;QAElE,oDAAoD;QACpD,MAAM,WAAW;YACf,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;YACjC,MAAM;gBACJ,SAAS;oBACP,aAAa,WAAW,CAAC,EAAE,EAAE,WAAW;oBACxC,oBAAoB;oBACpB,aAAa,IAAI,aAAa;gBAChC;gBACA,QAAQ;oBACN,QAAQ;wBACN;4BAAE,MAAM;4BAAc,QAAQ;wBAAI;wBAClC;4BAAE,MAAM;4BAAc,QAAQ;wBAAI;wBAClC;4BAAE,MAAM;4BAAc,QAAQ;wBAAG;wBACjC;4BAAE,MAAM;4BAAc,QAAQ;wBAAI;wBAClC;4BAAE,MAAM;4BAAc,QAAQ;wBAAI;qBACnC;oBACD,kBAAkB;wBAChB;4BAAE,MAAM;4BAAG,QAAQ;wBAAG;wBACtB;4BAAE,MAAM;4BAAG,QAAQ;wBAAG;wBACtB;4BAAE,MAAM;4BAAI,QAAQ;wBAAG;wBACvB;4BAAE,MAAM;4BAAI,QAAQ;wBAAG;wBACvB;4BAAE,MAAM;4BAAI,QAAQ;wBAAG;wBACvB;4BAAE,MAAM;4BAAI,QAAQ;wBAAG;wBACvB;4BAAE,MAAM;4BAAI,QAAQ;wBAAG;qBACxB;oBACD,eAAe;wBACb;4BAAE,WAAW;4BAAG,QAAQ;wBAAI;wBAC5B;4BAAE,WAAW;4BAAG,QAAQ;wBAAI;wBAC5B;4BAAE,WAAW;4BAAG,QAAQ;wBAAI;wBAC5B;4BAAE,WAAW;4BAAG,QAAQ;wBAAI;wBAC5B;4BAAE,WAAW;4BAAG,QAAQ;wBAAI;wBAC5B;4BAAE,WAAW;4BAAG,QAAQ;wBAAG;qBAC5B;gBACH;gBACA,cAAc;oBACZ,cAAc;wBACZ;4BAAE,MAAM;4BAAY,OAAO;4BAAK,YAAY;wBAAG;wBAC/C;4BAAE,MAAM;4BAAS,OAAO;4BAAK,YAAY;wBAAG;wBAC5C;4BAAE,MAAM;4BAAW,OAAO;4BAAK,YAAY;wBAAG;qBAC/C;oBACD,oBAAoB;wBAClB;4BAAE,MAAM;4BAAc,OAAO;4BAAK,YAAY;wBAAG;wBACjD;4BAAE,MAAM;4BAAU,OAAO;4BAAK,YAAY;wBAAG;qBAC9C;gBACH;gBACA,OAAO;oBACL;wBAAE,IAAI;wBAAU,QAAQ;wBAAK,aAAa;oBAAG;oBAC7C;wBAAE,IAAI;wBAAU,QAAQ;wBAAK,aAAa;oBAAG;oBAC7C;wBAAE,IAAI;wBAAU,QAAQ;wBAAK,aAAa;oBAAG;oBAC7C;wBAAE,IAAI;wBAAU,QAAQ;wBAAI,aAAa;oBAAG;iBAC7C;YACH;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}