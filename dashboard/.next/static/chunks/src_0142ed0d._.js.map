{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/dashboard/sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\nimport { cn } from \"@/lib/utils\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Sheet, SheetContent, SheetTrigger } from \"@/components/ui/sheet\";\nimport {\n  BarChart3,\n  Users,\n  MessageSquare,\n  Settings,\n  Menu,\n  Home,\n  TrendingUp,\n  UserCheck,\n} from \"lucide-react\";\n\nconst navigation = [\n  {\n    name: \"Dashboard\",\n    href: \"/\",\n    icon: Home,\n  },\n  {\n    name: \"Patient Analytics\",\n    href: \"/patients\",\n    icon: Users,\n  },\n  {\n    name: \"Service Analytics\",\n    href: \"/services\",\n    icon: BarChart3,\n  },\n  {\n    name: \"WhatsApp Campaigns\",\n    href: \"/campaigns\",\n    icon: MessageSquare,\n  },\n  {\n    name: \"Performance\",\n    href: \"/performance\",\n    icon: TrendingUp,\n  },\n  {\n    name: \"Customer Service\",\n    href: \"/customer-service\",\n    icon: UserChe<PERSON>,\n  },\n];\n\ninterface SidebarProps {\n  className?: string;\n}\n\nexport function Sidebar({ className }: SidebarProps) {\n  const pathname = usePathname();\n\n  return (\n    <div className={cn(\"pb-12 min-h-screen\", className)}>\n      <div className=\"space-y-4 py-4\">\n        <div className=\"px-3 py-2\">\n          <div className=\"mb-2 px-4 text-lg font-semibold tracking-tight\">\n            Adolfo Lutz\n          </div>\n          <div className=\"space-y-1\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={cn(\n                  \"flex items-center rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground transition-colors\",\n                  pathname === item.href\n                    ? \"bg-accent text-accent-foreground\"\n                    : \"text-muted-foreground\"\n                )}\n              >\n                <item.icon className=\"mr-2 h-4 w-4\" />\n                {item.name}\n              </Link>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport function MobileSidebar() {\n  const [open, setOpen] = useState(false);\n\n  return (\n    <Sheet open={open} onOpenChange={setOpen}>\n      <SheetTrigger asChild>\n        <Button variant=\"ghost\" size=\"icon\" className=\"md:hidden\">\n          <Menu className=\"h-5 w-5\" />\n          <span className=\"sr-only\">Toggle navigation menu</span>\n        </Button>\n      </SheetTrigger>\n      <SheetContent side=\"left\" className=\"w-64 p-0\">\n        <Sidebar />\n      </SheetContent>\n    </Sheet>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,OAAI;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;IACb;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qNAAA,CAAA,YAAS;IACjB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,2NAAA,CAAA,gBAAa;IACrB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qNAAA,CAAA,aAAU;IAClB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,mNAAA,CAAA,YAAS;IACjB;CACD;AAMM,SAAS,QAAQ,EAAE,SAAS,EAAgB;;IACjD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;kBACvC,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAiD;;;;;;kCAGhE,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6HACA,aAAa,KAAK,IAAI,GAClB,qCACA;;kDAGN,6LAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;oCACpB,KAAK,IAAI;;+BAVL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB9B;GA/BgB;;QACG,qIAAA,CAAA,cAAW;;;KADd;AAiCT,SAAS;;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,qBACE,6LAAC,oIAAA,CAAA,QAAK;QAAC,MAAM;QAAM,cAAc;;0BAC/B,6LAAC,oIAAA,CAAA,eAAY;gBAAC,OAAO;0BACnB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAO,WAAU;;sCAC5C,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,oIAAA,CAAA,eAAY;gBAAC,MAAK;gBAAO,WAAU;0BAClC,cAAA,6LAAC;;;;;;;;;;;;;;;;AAIT;IAhBgB;MAAA", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 537, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 833, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/dashboard/header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { MobileSidebar } from \"./sidebar\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { Bell, Settings, User } from \"lucide-react\";\n\nexport function Header() {\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container flex h-14 items-center\">\n        <MobileSidebar />\n        \n        <div className=\"mr-4 hidden md:flex\">\n          <div className=\"mr-6 flex items-center space-x-2\">\n            <span className=\"hidden font-bold sm:inline-block\">\n              Customer Service Dashboard\n            </span>\n          </div>\n        </div>\n\n        <div className=\"flex flex-1 items-center justify-between space-x-2 md:justify-end\">\n          <div className=\"w-full flex-1 md:w-auto md:flex-none\">\n            {/* Search could go here if needed */}\n          </div>\n          \n          <nav className=\"flex items-center space-x-2\">\n            <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n              <Bell className=\"h-4 w-4\" />\n              <Badge \n                variant=\"destructive\" \n                className=\"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center\"\n              >\n                3\n              </Badge>\n              <span className=\"sr-only\">Notifications</span>\n            </Button>\n            \n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button variant=\"ghost\" size=\"icon\">\n                  <User className=\"h-4 w-4\" />\n                  <span className=\"sr-only\">User menu</span>\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent align=\"end\">\n                <DropdownMenuLabel>My Account</DropdownMenuLabel>\n                <DropdownMenuSeparator />\n                <DropdownMenuItem>\n                  <Settings className=\"mr-2 h-4 w-4\" />\n                  Settings\n                </DropdownMenuItem>\n                <DropdownMenuSeparator />\n                <DropdownMenuItem>\n                  Log out\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </nav>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAbA;;;;;;;AAeO,SAAS;IACd,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6IAAA,CAAA,gBAAa;;;;;8BAEd,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAmC;;;;;;;;;;;;;;;;8BAMvD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCAIf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,WAAU;;sDAC5C,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;8CAG5B,6LAAC,+IAAA,CAAA,eAAY;;sDACX,6LAAC,+IAAA,CAAA,sBAAmB;4CAAC,OAAO;sDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;;kEAC3B,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;sDAG9B,6LAAC,+IAAA,CAAA,sBAAmB;4CAAC,OAAM;;8DACzB,6LAAC,+IAAA,CAAA,oBAAiB;8DAAC;;;;;;8DACnB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;8DACtB,6LAAC,+IAAA,CAAA,mBAAgB;;sEACf,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGvC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;8DACtB,6LAAC,+IAAA,CAAA,mBAAgB;8DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUlC;KAxDgB", "debugId": null}}, {"offset": {"line": 1057, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/charts/service-volume-chart.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Line<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, ResponsiveC<PERSON><PERSON> } from 'recharts';\n\nconst data = [\n  { date: '01/12', orders: 245 },\n  { date: '02/12', orders: 267 },\n  { date: '03/12', orders: 289 },\n  { date: '04/12', orders: 312 },\n  { date: '05/12', orders: 298 },\n  { date: '06/12', orders: 276 },\n  { date: '07/12', orders: 234 },\n  { date: '08/12', orders: 287 },\n  { date: '09/12', orders: 301 },\n  { date: '10/12', orders: 324 },\n  { date: '11/12', orders: 345 },\n  { date: '12/12', orders: 367 },\n  { date: '13/12', orders: 389 },\n  { date: '14/12', orders: 356 },\n  { date: '15/12', orders: 334 },\n  { date: '16/12', orders: 312 },\n  { date: '17/12', orders: 298 },\n  { date: '18/12', orders: 276 },\n  { date: '19/12', orders: 254 },\n  { date: '20/12', orders: 287 },\n  { date: '21/12', orders: 301 },\n  { date: '22/12', orders: 324 },\n  { date: '23/12', orders: 345 },\n  { date: '24/12', orders: 367 },\n  { date: '25/12', orders: 189 },\n  { date: '26/12', orders: 234 },\n  { date: '27/12', orders: 267 },\n  { date: '28/12', orders: 289 },\n  { date: '29/12', orders: 312 },\n  { date: '30/12', orders: 334 },\n];\n\nexport function ServiceVolumeChart() {\n  return (\n    <div className=\"h-[300px]\">\n      <ResponsiveContainer width=\"100%\" height=\"100%\">\n        <LineChart\n          data={data}\n          margin={{\n            top: 5,\n            right: 30,\n            left: 20,\n            bottom: 5,\n          }}\n        >\n          <CartesianGrid strokeDasharray=\"3 3\" />\n          <XAxis \n            dataKey=\"date\" \n            fontSize={12}\n            tick={{ fontSize: 10 }}\n          />\n          <YAxis />\n          <Tooltip />\n          <Line \n            type=\"monotone\" \n            dataKey=\"orders\" \n            stroke=\"#3B82F6\" \n            strokeWidth={2}\n            dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}\n            activeDot={{ r: 6 }}\n          />\n        </LineChart>\n      </ResponsiveContainer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;AAIA,MAAM,OAAO;IACX;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;IAC7B;QAAE,MAAM;QAAS,QAAQ;IAAI;CAC9B;AAEM,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;YAAC,OAAM;YAAO,QAAO;sBACvC,cAAA,6LAAC,wJAAA,CAAA,YAAS;gBACR,MAAM;gBACN,QAAQ;oBACN,KAAK;oBACL,OAAO;oBACP,MAAM;oBACN,QAAQ;gBACV;;kCAEA,6LAAC,gKAAA,CAAA,gBAAa;wBAAC,iBAAgB;;;;;;kCAC/B,6LAAC,wJAAA,CAAA,QAAK;wBACJ,SAAQ;wBACR,UAAU;wBACV,MAAM;4BAAE,UAAU;wBAAG;;;;;;kCAEvB,6LAAC,wJAAA,CAAA,QAAK;;;;;kCACN,6LAAC,0JAAA,CAAA,UAAO;;;;;kCACR,6LAAC,uJAAA,CAAA,OAAI;wBACH,MAAK;wBACL,SAAQ;wBACR,QAAO;wBACP,aAAa;wBACb,KAAK;4BAAE,MAAM;4BAAW,aAAa;4BAAG,GAAG;wBAAE;wBAC7C,WAAW;4BAAE,GAAG;wBAAE;;;;;;;;;;;;;;;;;;;;;;AAM9B;KAjCgB", "debugId": null}}, {"offset": {"line": 1283, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/charts/service-time-chart.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';\n\nconst data = [\n  { hour: '07:00', avgTime: 6.2 },\n  { hour: '08:00', avgTime: 8.5 },\n  { hour: '09:00', avgTime: 9.8 },\n  { hour: '10:00', avgTime: 7.3 },\n  { hour: '11:00', avgTime: 6.9 },\n  { hour: '12:00', avgTime: 5.4 },\n  { hour: '13:00', avgTime: 4.8 },\n  { hour: '14:00', avgTime: 8.9 },\n  { hour: '15:00', avgTime: 10.2 },\n  { hour: '16:00', avgTime: 9.1 },\n  { hour: '17:00', avgTime: 7.6 },\n  { hour: '18:00', avgTime: 6.3 },\n];\n\nexport function ServiceTimeChart() {\n  return (\n    <div className=\"h-[300px]\">\n      <ResponsiveContainer width=\"100%\" height=\"100%\">\n        <BarChart\n          data={data}\n          margin={{\n            top: 5,\n            right: 30,\n            left: 20,\n            bottom: 5,\n          }}\n        >\n          <CartesianGrid strokeDasharray=\"3 3\" />\n          <XAxis \n            dataKey=\"hour\" \n            fontSize={12}\n          />\n          <YAxis \n            label={{ value: 'Minutes', angle: -90, position: 'insideLeft' }}\n          />\n          <Tooltip \n            formatter={(value) => [`${value} min`, 'Avg. Service Time']}\n          />\n          <Bar \n            dataKey=\"avgTime\" \n            fill=\"#10B981\"\n            radius={[4, 4, 0, 0]}\n          />\n        </BarChart>\n      </ResponsiveContainer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;AAIA,MAAM,OAAO;IACX;QAAE,MAAM;QAAS,SAAS;IAAI;IAC9B;QAAE,MAAM;QAAS,SAAS;IAAI;IAC9B;QAAE,MAAM;QAAS,SAAS;IAAI;IAC9B;QAAE,MAAM;QAAS,SAAS;IAAI;IAC9B;QAAE,MAAM;QAAS,SAAS;IAAI;IAC9B;QAAE,MAAM;QAAS,SAAS;IAAI;IAC9B;QAAE,MAAM;QAAS,SAAS;IAAI;IAC9B;QAAE,MAAM;QAAS,SAAS;IAAI;IAC9B;QAAE,MAAM;QAAS,SAAS;IAAK;IAC/B;QAAE,MAAM;QAAS,SAAS;IAAI;IAC9B;QAAE,MAAM;QAAS,SAAS;IAAI;IAC9B;QAAE,MAAM;QAAS,SAAS;IAAI;CAC/B;AAEM,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;YAAC,OAAM;YAAO,QAAO;sBACvC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;gBACP,MAAM;gBACN,QAAQ;oBACN,KAAK;oBACL,OAAO;oBACP,MAAM;oBACN,QAAQ;gBACV;;kCAEA,6LAAC,gKAAA,CAAA,gBAAa;wBAAC,iBAAgB;;;;;;kCAC/B,6LAAC,wJAAA,CAAA,QAAK;wBACJ,SAAQ;wBACR,UAAU;;;;;;kCAEZ,6LAAC,wJAAA,CAAA,QAAK;wBACJ,OAAO;4BAAE,OAAO;4BAAW,OAAO,CAAC;4BAAI,UAAU;wBAAa;;;;;;kCAEhE,6LAAC,0JAAA,CAAA,UAAO;wBACN,WAAW,CAAC,QAAU;gCAAC,GAAG,MAAM,IAAI,CAAC;gCAAE;6BAAoB;;;;;;kCAE7D,6LAAC,sJAAA,CAAA,MAAG;wBACF,SAAQ;wBACR,MAAK;wBACL,QAAQ;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE;;;;;;;;;;;;;;;;;;;;;;AAMhC;KAjCgB", "debugId": null}}, {"offset": {"line": 1441, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/charts/service-type-chart.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend } from 'recharts';\n\nconst data = [\n  { name: 'Routine Exams', value: 45, color: '#3B82F6' },\n  { name: 'Emergency', value: 15, color: '#EF4444' },\n  { name: 'Preventive Care', value: 25, color: '#10B981' },\n  { name: 'Follow-up', value: 10, color: '#F59E0B' },\n  { name: 'Specialized Tests', value: 5, color: '#8B5CF6' },\n];\n\nconst COLORS = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6'];\n\nexport function ServiceTypeChart() {\n  return (\n    <div className=\"h-[300px]\">\n      <ResponsiveContainer width=\"100%\" height=\"100%\">\n        <PieChart>\n          <Pie\n            data={data}\n            cx=\"50%\"\n            cy=\"50%\"\n            labelLine={false}\n            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n            outerRadius={80}\n            fill=\"#8884d8\"\n            dataKey=\"value\"\n          >\n            {data.map((entry, index) => (\n              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\n            ))}\n          </Pie>\n          <Tooltip />\n          <Legend />\n        </PieChart>\n      </ResponsiveContainer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;AAIA,MAAM,OAAO;IACX;QAAE,MAAM;QAAiB,OAAO;QAAI,OAAO;IAAU;IACrD;QAAE,MAAM;QAAa,OAAO;QAAI,OAAO;IAAU;IACjD;QAAE,MAAM;QAAmB,OAAO;QAAI,OAAO;IAAU;IACvD;QAAE,MAAM;QAAa,OAAO;QAAI,OAAO;IAAU;IACjD;QAAE,MAAM;QAAqB,OAAO;QAAG,OAAO;IAAU;CACzD;AAED,MAAM,SAAS;IAAC;IAAW;IAAW;IAAW;IAAW;CAAU;AAE/D,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;YAAC,OAAM;YAAO,QAAO;sBACvC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;;kCACP,6LAAC,kJAAA,CAAA,MAAG;wBACF,MAAM;wBACN,IAAG;wBACH,IAAG;wBACH,WAAW;wBACX,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAK,GAAG,KAAK,CAAC,EAAE,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;wBACtE,aAAa;wBACb,MAAK;wBACL,SAAQ;kCAEP,KAAK,GAAG,CAAC,CAAC,OAAO,sBAChB,6LAAC,uJAAA,CAAA,OAAI;gCAAuB,MAAM,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;+BAApD,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;kCAG9B,6LAAC,0JAAA,CAAA,UAAO;;;;;kCACR,6LAAC,yJAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;AAKjB;KAzBgB", "debugId": null}}, {"offset": {"line": 1556, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/charts/service-weekday-chart.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';\n\nconst data = [\n  { day: 'Mon', orders: 1245 },\n  { day: 'Tue', orders: 1189 },\n  { day: 'Wed', orders: 1067 },\n  { day: 'Thu', orders: 1134 },\n  { day: 'Fri', orders: 1298 },\n  { day: 'Sat', orders: 856 },\n  { day: 'Sun', orders: 234 },\n];\n\nexport function ServiceWeekdayChart() {\n  return (\n    <div className=\"h-[300px]\">\n      <ResponsiveContainer width=\"100%\" height=\"100%\">\n        <BarChart\n          data={data}\n          margin={{\n            top: 5,\n            right: 30,\n            left: 20,\n            bottom: 5,\n          }}\n        >\n          <CartesianGrid strokeDasharray=\"3 3\" />\n          <XAxis dataKey=\"day\" />\n          <YAxis />\n          <Tooltip \n            formatter={(value) => [`${value}`, 'Service Orders']}\n          />\n          <Bar \n            dataKey=\"orders\" \n            fill=\"#8B5CF6\"\n            radius={[4, 4, 0, 0]}\n          />\n        </BarChart>\n      </ResponsiveContainer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;AAIA,MAAM,OAAO;IACX;QAAE,KAAK;QAAO,QAAQ;IAAK;IAC3B;QAAE,KAAK;QAAO,QAAQ;IAAK;IAC3B;QAAE,KAAK;QAAO,QAAQ;IAAK;IAC3B;QAAE,KAAK;QAAO,QAAQ;IAAK;IAC3B;QAAE,KAAK;QAAO,QAAQ;IAAK;IAC3B;QAAE,KAAK;QAAO,QAAQ;IAAI;IAC1B;QAAE,KAAK;QAAO,QAAQ;IAAI;CAC3B;AAEM,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;YAAC,OAAM;YAAO,QAAO;sBACvC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;gBACP,MAAM;gBACN,QAAQ;oBACN,KAAK;oBACL,OAAO;oBACP,MAAM;oBACN,QAAQ;gBACV;;kCAEA,6LAAC,gKAAA,CAAA,gBAAa;wBAAC,iBAAgB;;;;;;kCAC/B,6LAAC,wJAAA,CAAA,QAAK;wBAAC,SAAQ;;;;;;kCACf,6LAAC,wJAAA,CAAA,QAAK;;;;;kCACN,6LAAC,0JAAA,CAAA,UAAO;wBACN,WAAW,CAAC,QAAU;gCAAC,GAAG,OAAO;gCAAE;6BAAiB;;;;;;kCAEtD,6LAAC,sJAAA,CAAA,MAAG;wBACF,SAAQ;wBACR,MAAK;wBACL,QAAQ;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE;;;;;;;;;;;;;;;;;;;;;;AAMhC;KA5BgB", "debugId": null}}]}