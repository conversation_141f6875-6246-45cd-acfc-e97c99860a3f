{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/dashboard/sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\nimport { cn } from \"@/lib/utils\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Sheet, SheetContent, SheetTrigger } from \"@/components/ui/sheet\";\nimport {\n  BarChart3,\n  Users,\n  MessageSquare,\n  Settings,\n  Menu,\n  Home,\n  TrendingUp,\n  UserCheck,\n} from \"lucide-react\";\n\nconst navigation = [\n  {\n    name: \"Dashboard\",\n    href: \"/\",\n    icon: Home,\n  },\n  {\n    name: \"Patient Analytics\",\n    href: \"/patients\",\n    icon: Users,\n  },\n  {\n    name: \"Service Analytics\",\n    href: \"/services\",\n    icon: BarChart3,\n  },\n  {\n    name: \"WhatsApp Campaigns\",\n    href: \"/campaigns\",\n    icon: MessageSquare,\n  },\n  {\n    name: \"Performance\",\n    href: \"/performance\",\n    icon: TrendingUp,\n  },\n  {\n    name: \"Customer Service\",\n    href: \"/customer-service\",\n    icon: UserChe<PERSON>,\n  },\n];\n\ninterface SidebarProps {\n  className?: string;\n}\n\nexport function Sidebar({ className }: SidebarProps) {\n  const pathname = usePathname();\n\n  return (\n    <div className={cn(\"pb-12 min-h-screen\", className)}>\n      <div className=\"space-y-4 py-4\">\n        <div className=\"px-3 py-2\">\n          <div className=\"mb-2 px-4 text-lg font-semibold tracking-tight\">\n            Adolfo Lutz\n          </div>\n          <div className=\"space-y-1\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={cn(\n                  \"flex items-center rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground transition-colors\",\n                  pathname === item.href\n                    ? \"bg-accent text-accent-foreground\"\n                    : \"text-muted-foreground\"\n                )}\n              >\n                <item.icon className=\"mr-2 h-4 w-4\" />\n                {item.name}\n              </Link>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport function MobileSidebar() {\n  const [open, setOpen] = useState(false);\n\n  return (\n    <Sheet open={open} onOpenChange={setOpen}>\n      <SheetTrigger asChild>\n        <Button variant=\"ghost\" size=\"icon\" className=\"md:hidden\">\n          <Menu className=\"h-5 w-5\" />\n          <span className=\"sr-only\">Toggle navigation menu</span>\n        </Button>\n      </SheetTrigger>\n      <SheetContent side=\"left\" className=\"w-64 p-0\">\n        <Sidebar />\n      </SheetContent>\n    </Sheet>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,OAAI;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;IACb;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qNAAA,CAAA,YAAS;IACjB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,2NAAA,CAAA,gBAAa;IACrB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qNAAA,CAAA,aAAU;IAClB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,mNAAA,CAAA,YAAS;IACjB;CACD;AAMM,SAAS,QAAQ,EAAE,SAAS,EAAgB;;IACjD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;kBACvC,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAiD;;;;;;kCAGhE,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6HACA,aAAa,KAAK,IAAI,GAClB,qCACA;;kDAGN,6LAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;oCACpB,KAAK,IAAI;;+BAVL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB9B;GA/BgB;;QACG,qIAAA,CAAA,cAAW;;;KADd;AAiCT,SAAS;;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,qBACE,6LAAC,oIAAA,CAAA,QAAK;QAAC,MAAM;QAAM,cAAc;;0BAC/B,6LAAC,oIAAA,CAAA,eAAY;gBAAC,OAAO;0BACnB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAO,WAAU;;sCAC5C,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,oIAAA,CAAA,eAAY;gBAAC,MAAK;gBAAO,WAAU;0BAClC,cAAA,6LAAC;;;;;;;;;;;;;;;;AAIT;IAhBgB;MAAA", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 537, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 833, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/dashboard/header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { MobileSidebar } from \"./sidebar\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { Bell, Settings, User } from \"lucide-react\";\n\nexport function Header() {\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container flex h-14 items-center\">\n        <MobileSidebar />\n        \n        <div className=\"mr-4 hidden md:flex\">\n          <div className=\"mr-6 flex items-center space-x-2\">\n            <span className=\"hidden font-bold sm:inline-block\">\n              Customer Service Dashboard\n            </span>\n          </div>\n        </div>\n\n        <div className=\"flex flex-1 items-center justify-between space-x-2 md:justify-end\">\n          <div className=\"w-full flex-1 md:w-auto md:flex-none\">\n            {/* Search could go here if needed */}\n          </div>\n          \n          <nav className=\"flex items-center space-x-2\">\n            <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n              <Bell className=\"h-4 w-4\" />\n              <Badge \n                variant=\"destructive\" \n                className=\"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center\"\n              >\n                3\n              </Badge>\n              <span className=\"sr-only\">Notifications</span>\n            </Button>\n            \n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button variant=\"ghost\" size=\"icon\">\n                  <User className=\"h-4 w-4\" />\n                  <span className=\"sr-only\">User menu</span>\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent align=\"end\">\n                <DropdownMenuLabel>My Account</DropdownMenuLabel>\n                <DropdownMenuSeparator />\n                <DropdownMenuItem>\n                  <Settings className=\"mr-2 h-4 w-4\" />\n                  Settings\n                </DropdownMenuItem>\n                <DropdownMenuSeparator />\n                <DropdownMenuItem>\n                  Log out\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </nav>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAbA;;;;;;;AAeO,SAAS;IACd,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6IAAA,CAAA,gBAAa;;;;;8BAEd,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAmC;;;;;;;;;;;;;;;;8BAMvD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCAIf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,WAAU;;sDAC5C,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;8CAG5B,6LAAC,+IAAA,CAAA,eAAY;;sDACX,6LAAC,+IAAA,CAAA,sBAAmB;4CAAC,OAAO;sDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;;kEAC3B,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;sDAG9B,6LAAC,+IAAA,CAAA,sBAAmB;4CAAC,OAAM;;8DACzB,6LAAC,+IAAA,CAAA,oBAAiB;8DAAC;;;;;;8DACnB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;8DACtB,6LAAC,+IAAA,CAAA,mBAAgB;;sEACf,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGvC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;8DACtB,6LAAC,+IAAA,CAAA,mBAAgB;8DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUlC;KAxDgB", "debugId": null}}, {"offset": {"line": 1057, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/dashboard/layout.tsx"], "sourcesContent": ["import { Header } from \"./header\";\nimport { Sidebar } from \"./sidebar\";\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Header />\n      <div className=\"flex\">\n        <aside className=\"hidden w-64 border-r bg-background md:block\">\n          <Sidebar />\n        </aside>\n        <main className=\"flex-1 p-6\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMO,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,4IAAA,CAAA,SAAM;;;;;0BACP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCACf,cAAA,6LAAC,6IAAA,CAAA,UAAO;;;;;;;;;;kCAEV,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;KAdgB", "debugId": null}}, {"offset": {"line": 1123, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 1238, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/charts/patient-age-chart.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveC<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend } from 'recharts';\n\nconst COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];\n\ninterface AgeData {\n  ageGroup: string;\n  count: number;\n}\n\ninterface PatientAgeChartProps {\n  data?: AgeData[];\n}\n\nexport function PatientAgeChart({ data }: PatientAgeChartProps) {\n  // Transform data for the chart\n  const chartData = data?.map((item, index) => ({\n    name: item.ageGroup,\n    value: item.count,\n    color: COLORS[index % COLORS.length]\n  })) || [];\n  if (!chartData.length) {\n    return (\n      <div className=\"h-[300px] flex items-center justify-center text-muted-foreground\">\n        No age distribution data available\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-[300px]\">\n      <ResponsiveContainer width=\"100%\" height=\"100%\">\n        <PieChart>\n          <Pie\n            data={chartData}\n            cx=\"50%\"\n            cy=\"50%\"\n            labelLine={false}\n            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n            outerRadius={80}\n            fill=\"#8884d8\"\n            dataKey=\"value\"\n          >\n            {chartData.map((entry, index) => (\n              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\n            ))}\n          </Pie>\n          <Tooltip />\n          <Legend />\n        </PieChart>\n      </ResponsiveContainer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;AAIA,MAAM,SAAS;IAAC;IAAW;IAAW;IAAW;IAAW;CAAU;AAW/D,SAAS,gBAAgB,EAAE,IAAI,EAAwB;IAC5D,+BAA+B;IAC/B,MAAM,YAAY,MAAM,IAAI,CAAC,MAAM,QAAU,CAAC;YAC5C,MAAM,KAAK,QAAQ;YACnB,OAAO,KAAK,KAAK;YACjB,OAAO,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;QACtC,CAAC,MAAM,EAAE;IACT,IAAI,CAAC,UAAU,MAAM,EAAE;QACrB,qBACE,6LAAC;YAAI,WAAU;sBAAmE;;;;;;IAItF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;YAAC,OAAM;YAAO,QAAO;sBACvC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;;kCACP,6LAAC,kJAAA,CAAA,MAAG;wBACF,MAAM;wBACN,IAAG;wBACH,IAAG;wBACH,WAAW;wBACX,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAK,GAAG,KAAK,CAAC,EAAE,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;wBACtE,aAAa;wBACb,MAAK;wBACL,SAAQ;kCAEP,UAAU,GAAG,CAAC,CAAC,OAAO,sBACrB,6LAAC,uJAAA,CAAA,OAAI;gCAAuB,MAAM,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;+BAApD,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;kCAG9B,6LAAC,0JAAA,CAAA,UAAO;;;;;kCACR,6LAAC,yJAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;AAKjB;KAvCgB", "debugId": null}}, {"offset": {"line": 1342, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/charts/patient-gender-chart.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveC<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend } from 'recharts';\n\nconst COLORS = ['#EC4899', '#3B82F6'];\n\ninterface GenderData {\n  gender: string;\n  count: number;\n}\n\ninterface PatientGenderChartProps {\n  data?: GenderData[];\n}\n\nexport function PatientGenderChart({ data }: PatientGenderChartProps) {\n  // Transform data for the chart\n  const chartData = data?.map((item, index) => ({\n    name: item.gender === 'M' ? 'Male' : item.gender === 'F' ? 'Female' : item.gender,\n    value: item.count,\n    color: COLORS[index % COLORS.length]\n  })) || [];\n  if (!chartData.length) {\n    return (\n      <div className=\"h-[300px] flex items-center justify-center text-muted-foreground\">\n        No gender distribution data available\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-[300px]\">\n      <ResponsiveContainer width=\"100%\" height=\"100%\">\n        <PieChart>\n          <Pie\n            data={chartData}\n            cx=\"50%\"\n            cy=\"50%\"\n            labelLine={false}\n            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n            outerRadius={80}\n            fill=\"#8884d8\"\n            dataKey=\"value\"\n          >\n            {chartData.map((entry, index) => (\n              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\n            ))}\n          </Pie>\n          <Tooltip />\n          <Legend />\n        </PieChart>\n      </ResponsiveContainer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;AAIA,MAAM,SAAS;IAAC;IAAW;CAAU;AAW9B,SAAS,mBAAmB,EAAE,IAAI,EAA2B;IAClE,+BAA+B;IAC/B,MAAM,YAAY,MAAM,IAAI,CAAC,MAAM,QAAU,CAAC;YAC5C,MAAM,KAAK,MAAM,KAAK,MAAM,SAAS,KAAK,MAAM,KAAK,MAAM,WAAW,KAAK,MAAM;YACjF,OAAO,KAAK,KAAK;YACjB,OAAO,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;QACtC,CAAC,MAAM,EAAE;IACT,IAAI,CAAC,UAAU,MAAM,EAAE;QACrB,qBACE,6LAAC;YAAI,WAAU;sBAAmE;;;;;;IAItF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;YAAC,OAAM;YAAO,QAAO;sBACvC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;;kCACP,6LAAC,kJAAA,CAAA,MAAG;wBACF,MAAM;wBACN,IAAG;wBACH,IAAG;wBACH,WAAW;wBACX,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAK,GAAG,KAAK,CAAC,EAAE,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;wBACtE,aAAa;wBACb,MAAK;wBACL,SAAQ;kCAEP,UAAU,GAAG,CAAC,CAAC,OAAO,sBACrB,6LAAC,uJAAA,CAAA,OAAI;gCAAuB,MAAM,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;+BAApD,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;kCAG9B,6LAAC,0JAAA,CAAA,UAAO;;;;;kCACR,6LAAC,yJAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;AAKjB;KAvCgB", "debugId": null}}, {"offset": {"line": 1443, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/charts/patient-location-chart.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';\n\ninterface LocationData {\n  city: string;\n  count: number;\n}\n\ninterface PatientLocationChartProps {\n  data?: LocationData[];\n}\n\nexport function PatientLocationChart({ data }: PatientLocationChartProps) {\n  // Transform data for the chart\n  const chartData = data?.map(item => ({\n    city: item.city,\n    patients: item.count\n  })) || [];\n  if (!chartData.length) {\n    return (\n      <div className=\"h-[300px] flex items-center justify-center text-muted-foreground\">\n        No location distribution data available\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-[300px]\">\n      <ResponsiveContainer width=\"100%\" height=\"100%\">\n        <BarChart\n          data={chartData}\n          margin={{\n            top: 5,\n            right: 30,\n            left: 20,\n            bottom: 5,\n          }}\n        >\n          <CartesianGrid strokeDasharray=\"3 3\" />\n          <XAxis\n            dataKey=\"city\"\n            angle={-45}\n            textAnchor=\"end\"\n            height={80}\n            fontSize={12}\n          />\n          <YAxis />\n          <Tooltip />\n          <Bar dataKey=\"patients\" fill=\"#3B82F6\" />\n        </BarChart>\n      </ResponsiveContainer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;AAaO,SAAS,qBAAqB,EAAE,IAAI,EAA6B;IACtE,+BAA+B;IAC/B,MAAM,YAAY,MAAM,IAAI,CAAA,OAAQ,CAAC;YACnC,MAAM,KAAK,IAAI;YACf,UAAU,KAAK,KAAK;QACtB,CAAC,MAAM,EAAE;IACT,IAAI,CAAC,UAAU,MAAM,EAAE;QACrB,qBACE,6LAAC;YAAI,WAAU;sBAAmE;;;;;;IAItF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;YAAC,OAAM;YAAO,QAAO;sBACvC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;gBACP,MAAM;gBACN,QAAQ;oBACN,KAAK;oBACL,OAAO;oBACP,MAAM;oBACN,QAAQ;gBACV;;kCAEA,6LAAC,gKAAA,CAAA,gBAAa;wBAAC,iBAAgB;;;;;;kCAC/B,6LAAC,wJAAA,CAAA,QAAK;wBACJ,SAAQ;wBACR,OAAO,CAAC;wBACR,YAAW;wBACX,QAAQ;wBACR,UAAU;;;;;;kCAEZ,6LAAC,wJAAA,CAAA,QAAK;;;;;kCACN,6LAAC,0JAAA,CAAA,UAAO;;;;;kCACR,6LAAC,sJAAA,CAAA,MAAG;wBAAC,SAAQ;wBAAW,MAAK;;;;;;;;;;;;;;;;;;;;;;AAKvC;KAzCgB", "debugId": null}}, {"offset": {"line": 1552, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/components/charts/patient-activity-chart.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';\n\nconst data = [\n  { month: 'Jan', newPatients: 65, returningPatients: 180 },\n  { month: 'Feb', newPatients: 59, returningPatients: 195 },\n  { month: 'Mar', newPatients: 80, returningPatients: 210 },\n  { month: 'Apr', newPatients: 81, returningPatients: 225 },\n  { month: 'May', newPatients: 56, returningPatients: 240 },\n  { month: 'Jun', newPatients: 55, returningPatients: 255 },\n  { month: 'Jul', newPatients: 40, returningPatients: 270 },\n  { month: 'Aug', newPatients: 45, returningPatients: 285 },\n  { month: 'Sep', newPatients: 60, returningPatients: 300 },\n  { month: 'Oct', newPatients: 70, returningPatients: 315 },\n  { month: 'Nov', newPatients: 85, returningPatients: 330 },\n  { month: 'Dec', newPatients: 90, returningPatients: 345 },\n];\n\nexport function PatientActivity<PERSON><PERSON>() {\n  return (\n    <div className=\"h-[300px]\">\n      <ResponsiveContainer width=\"100%\" height=\"100%\">\n        <LineChart\n          data={data}\n          margin={{\n            top: 5,\n            right: 30,\n            left: 20,\n            bottom: 5,\n          }}\n        >\n          <CartesianGrid strokeDasharray=\"3 3\" />\n          <XAxis dataKey=\"month\" />\n          <YAxis />\n          <Tooltip />\n          <Legend />\n          <Line \n            type=\"monotone\" \n            dataKey=\"newPatients\" \n            stroke=\"#10B981\" \n            strokeWidth={2}\n            name=\"New Patients\"\n          />\n          <Line \n            type=\"monotone\" \n            dataKey=\"returningPatients\" \n            stroke=\"#3B82F6\" \n            strokeWidth={2}\n            name=\"Returning Patients\"\n          />\n        </LineChart>\n      </ResponsiveContainer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;AAIA,MAAM,OAAO;IACX;QAAE,OAAO;QAAO,aAAa;QAAI,mBAAmB;IAAI;IACxD;QAAE,OAAO;QAAO,aAAa;QAAI,mBAAmB;IAAI;IACxD;QAAE,OAAO;QAAO,aAAa;QAAI,mBAAmB;IAAI;IACxD;QAAE,OAAO;QAAO,aAAa;QAAI,mBAAmB;IAAI;IACxD;QAAE,OAAO;QAAO,aAAa;QAAI,mBAAmB;IAAI;IACxD;QAAE,OAAO;QAAO,aAAa;QAAI,mBAAmB;IAAI;IACxD;QAAE,OAAO;QAAO,aAAa;QAAI,mBAAmB;IAAI;IACxD;QAAE,OAAO;QAAO,aAAa;QAAI,mBAAmB;IAAI;IACxD;QAAE,OAAO;QAAO,aAAa;QAAI,mBAAmB;IAAI;IACxD;QAAE,OAAO;QAAO,aAAa;QAAI,mBAAmB;IAAI;IACxD;QAAE,OAAO;QAAO,aAAa;QAAI,mBAAmB;IAAI;IACxD;QAAE,OAAO;QAAO,aAAa;QAAI,mBAAmB;IAAI;CACzD;AAEM,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;YAAC,OAAM;YAAO,QAAO;sBACvC,cAAA,6LAAC,wJAAA,CAAA,YAAS;gBACR,MAAM;gBACN,QAAQ;oBACN,KAAK;oBACL,OAAO;oBACP,MAAM;oBACN,QAAQ;gBACV;;kCAEA,6LAAC,gKAAA,CAAA,gBAAa;wBAAC,iBAAgB;;;;;;kCAC/B,6LAAC,wJAAA,CAAA,QAAK;wBAAC,SAAQ;;;;;;kCACf,6LAAC,wJAAA,CAAA,QAAK;;;;;kCACN,6LAAC,0JAAA,CAAA,UAAO;;;;;kCACR,6LAAC,yJAAA,CAAA,SAAM;;;;;kCACP,6LAAC,uJAAA,CAAA,OAAI;wBACH,MAAK;wBACL,SAAQ;wBACR,QAAO;wBACP,aAAa;wBACb,MAAK;;;;;;kCAEP,6LAAC,uJAAA,CAAA,OAAI;wBACH,MAAK;wBACL,SAAQ;wBACR,QAAO;wBACP,aAAa;wBACb,MAAK;;;;;;;;;;;;;;;;;;;;;;AAMjB;KApCgB", "debugId": null}}, {"offset": {"line": 1724, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/lib/api.ts"], "sourcesContent": ["// API client for dashboard data fetching\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '';\n\n// Generic API fetch function with error handling\nasync function apiRequest<T>(endpoint: string, options?: RequestInit): Promise<T> {\n  try {\n    const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`;\n    \n    const response = await fetch(url, {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options?.headers,\n      },\n      ...options,\n    });\n\n    if (!response.ok) {\n      throw new Error(`API request failed: ${response.status} ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    \n    if (!data.success) {\n      throw new Error(data.message || 'API request failed');\n    }\n\n    return data.data;\n  } catch (error) {\n    console.error(`API request error for ${endpoint}:`, error);\n    throw error;\n  }\n}\n\n// Patient Analytics API\nexport const patientApi = {\n  async getAnalytics(params?: { startDate?: string; endDate?: string }) {\n    const searchParams = new URLSearchParams();\n    if (params?.startDate) searchParams.set('startDate', params.startDate);\n    if (params?.endDate) searchParams.set('endDate', params.endDate);\n    \n    const query = searchParams.toString();\n    return apiRequest(`/api/patients${query ? `?${query}` : ''}`);\n  }\n};\n\n// Service Analytics API\nexport const serviceApi = {\n  async getAnalytics(params?: { startDate?: string; endDate?: string }) {\n    const searchParams = new URLSearchParams();\n    if (params?.startDate) searchParams.set('startDate', params.startDate);\n    if (params?.endDate) searchParams.set('endDate', params.endDate);\n    \n    const query = searchParams.toString();\n    return apiRequest(`/api/services${query ? `?${query}` : ''}`);\n  }\n};\n\n// Campaign API\nexport const campaignApi = {\n  async getStats() {\n    return apiRequest('/api/campaigns?action=stats');\n  },\n\n  async getCities() {\n    return apiRequest('/api/campaigns?action=cities');\n  },\n\n  async getEligiblePatients(filters?: {\n    city?: string;\n    minAge?: number;\n    maxAge?: number;\n    lastVisitDays?: number;\n    search?: string;\n    limit?: number;\n  }) {\n    const searchParams = new URLSearchParams({ action: 'patients' });\n    \n    if (filters?.city) searchParams.set('city', filters.city);\n    if (filters?.minAge) searchParams.set('minAge', filters.minAge.toString());\n    if (filters?.maxAge) searchParams.set('maxAge', filters.maxAge.toString());\n    if (filters?.lastVisitDays) searchParams.set('lastVisitDays', filters.lastVisitDays.toString());\n    if (filters?.search) searchParams.set('search', filters.search);\n    if (filters?.limit) searchParams.set('limit', filters.limit.toString());\n    \n    return apiRequest(`/api/campaigns?${searchParams.toString()}`);\n  }\n};\n\n// Performance Analytics API\nexport const performanceApi = {\n  async getAnalytics(params?: { startDate?: string; endDate?: string }) {\n    const searchParams = new URLSearchParams();\n    if (params?.startDate) searchParams.set('startDate', params.startDate);\n    if (params?.endDate) searchParams.set('endDate', params.endDate);\n    \n    const query = searchParams.toString();\n    return apiRequest(`/api/performance${query ? `?${query}` : ''}`);\n  }\n};\n\n// Customer Service Analytics API\nexport const customerServiceApi = {\n  async getAnalytics(params?: { startDate?: string; endDate?: string }) {\n    const searchParams = new URLSearchParams();\n    if (params?.startDate) searchParams.set('startDate', params.startDate);\n    if (params?.endDate) searchParams.set('endDate', params.endDate);\n    \n    const query = searchParams.toString();\n    return apiRequest(`/api/customer-service${query ? `?${query}` : ''}`);\n  }\n};\n\n// Dashboard Overview API (combines multiple endpoints)\nexport const dashboardApi = {\n  async getOverview() {\n    try {\n      const [patients, services, campaigns, performance] = await Promise.allSettled([\n        patientApi.getAnalytics(),\n        serviceApi.getAnalytics(),\n        campaignApi.getStats(),\n        performanceApi.getAnalytics()\n      ]);\n\n      return {\n        patients: patients.status === 'fulfilled' ? patients.value : null,\n        services: services.status === 'fulfilled' ? services.value : null,\n        campaigns: campaigns.status === 'fulfilled' ? campaigns.value : null,\n        performance: performance.status === 'fulfilled' ? performance.value : null,\n        errors: [\n          ...(patients.status === 'rejected' ? [{ section: 'patients', error: patients.reason }] : []),\n          ...(services.status === 'rejected' ? [{ section: 'services', error: services.reason }] : []),\n          ...(campaigns.status === 'rejected' ? [{ section: 'campaigns', error: campaigns.reason }] : []),\n          ...(performance.status === 'rejected' ? [{ section: 'performance', error: performance.reason }] : [])\n        ]\n      };\n    } catch (error) {\n      console.error('Dashboard overview error:', error);\n      throw error;\n    }\n  }\n};\n\n// Types for API responses\nexport interface PatientAnalytics {\n  metrics: {\n    activePatients: number;\n    newPatients: number;\n  };\n  demographics: {\n    age: Array<{ ageGroup: string; count: number }>;\n    gender: Array<{ gender: string; count: number }>;\n    location: Array<{ city: string; count: number }>;\n  };\n  topPatients: Array<{\n    id: string;\n    name: string;\n    cpf: string;\n    visitCount: number;\n    lastVisit: string;\n  }>;\n  activityTrend: Array<{\n    date: string;\n    activePatients: number;\n    totalOrders: number;\n  }>;\n}\n\nexport interface ServiceAnalytics {\n  metrics: {\n    totalOrders: number;\n    averageServiceTime: number;\n    reprintRate: number;\n  };\n  trends: {\n    volume: Array<{ date: string; orders: number }>;\n    timeDistribution: Array<{ hour: number; orders: number }>;\n    weeklyPattern: Array<{ dayOfWeek: number; orders: number }>;\n  };\n  distribution: {\n    serviceTypes: Array<{ type: string; count: number; percentage: number }>;\n    onlineVsPresential: Array<{ type: string; count: number; percentage: number }>;\n  };\n  staff: Array<{\n    id: string;\n    orders: number;\n    averageTime: number;\n  }>;\n}\n\nexport interface CampaignStats {\n  stats: {\n    totalEligible: number;\n    lastWeek: number;\n    lastMonth: number;\n  };\n  recentCampaigns: Array<{\n    id: number;\n    name: string;\n    status: string;\n    recipients: number;\n    responses: number;\n    responseRate: number;\n    cost: number;\n    createdAt: string;\n  }>;\n}\n\nexport interface EligiblePatients {\n  patients: Array<{\n    id: string;\n    codigoOs: string;\n    data: string;\n    pacienteId: string;\n    nome: string;\n    email?: string;\n    telefone: string;\n    whatsapp: string;\n    cidade?: string;\n    idade?: number;\n    totalVisitas: number;\n  }>;\n  total: number;\n  filters: Record<string, any>;\n}\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;;;;;AAEpB;AAArB,MAAM,eAAe,6DAAmC;AAExD,iDAAiD;AACjD,eAAe,WAAc,QAAgB,EAAE,OAAqB;IAClE,IAAI;QACF,MAAM,MAAM,SAAS,UAAU,CAAC,UAAU,WAAW,GAAG,eAAe,UAAU;QAEjF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,SAAS;gBACP,gBAAgB;gBAChB,GAAG,SAAS,OAAO;YACrB;YACA,GAAG,OAAO;QACZ;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACjF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,KAAK,OAAO,EAAE;YACjB,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;QAClC;QAEA,OAAO,KAAK,IAAI;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,SAAS,CAAC,CAAC,EAAE;QACpD,MAAM;IACR;AACF;AAGO,MAAM,aAAa;IACxB,MAAM,cAAa,MAAiD;QAClE,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,WAAW,aAAa,GAAG,CAAC,aAAa,OAAO,SAAS;QACrE,IAAI,QAAQ,SAAS,aAAa,GAAG,CAAC,WAAW,OAAO,OAAO;QAE/D,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,WAAW,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC9D;AACF;AAGO,MAAM,aAAa;IACxB,MAAM,cAAa,MAAiD;QAClE,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,WAAW,aAAa,GAAG,CAAC,aAAa,OAAO,SAAS;QACrE,IAAI,QAAQ,SAAS,aAAa,GAAG,CAAC,WAAW,OAAO,OAAO;QAE/D,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,WAAW,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAC9D;AACF;AAGO,MAAM,cAAc;IACzB,MAAM;QACJ,OAAO,WAAW;IACpB;IAEA,MAAM;QACJ,OAAO,WAAW;IACpB;IAEA,MAAM,qBAAoB,OAOzB;QACC,MAAM,eAAe,IAAI,gBAAgB;YAAE,QAAQ;QAAW;QAE9D,IAAI,SAAS,MAAM,aAAa,GAAG,CAAC,QAAQ,QAAQ,IAAI;QACxD,IAAI,SAAS,QAAQ,aAAa,GAAG,CAAC,UAAU,QAAQ,MAAM,CAAC,QAAQ;QACvE,IAAI,SAAS,QAAQ,aAAa,GAAG,CAAC,UAAU,QAAQ,MAAM,CAAC,QAAQ;QACvE,IAAI,SAAS,eAAe,aAAa,GAAG,CAAC,iBAAiB,QAAQ,aAAa,CAAC,QAAQ;QAC5F,IAAI,SAAS,QAAQ,aAAa,GAAG,CAAC,UAAU,QAAQ,MAAM;QAC9D,IAAI,SAAS,OAAO,aAAa,GAAG,CAAC,SAAS,QAAQ,KAAK,CAAC,QAAQ;QAEpE,OAAO,WAAW,CAAC,eAAe,EAAE,aAAa,QAAQ,IAAI;IAC/D;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,cAAa,MAAiD;QAClE,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,WAAW,aAAa,GAAG,CAAC,aAAa,OAAO,SAAS;QACrE,IAAI,QAAQ,SAAS,aAAa,GAAG,CAAC,WAAW,OAAO,OAAO;QAE/D,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,WAAW,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACjE;AACF;AAGO,MAAM,qBAAqB;IAChC,MAAM,cAAa,MAAiD;QAClE,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,WAAW,aAAa,GAAG,CAAC,aAAa,OAAO,SAAS;QACrE,IAAI,QAAQ,SAAS,aAAa,GAAG,CAAC,WAAW,OAAO,OAAO;QAE/D,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,WAAW,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACtE;AACF;AAGO,MAAM,eAAe;IAC1B,MAAM;QACJ,IAAI;YACF,MAAM,CAAC,UAAU,UAAU,WAAW,YAAY,GAAG,MAAM,QAAQ,UAAU,CAAC;gBAC5E,WAAW,YAAY;gBACvB,WAAW,YAAY;gBACvB,YAAY,QAAQ;gBACpB,eAAe,YAAY;aAC5B;YAED,OAAO;gBACL,UAAU,SAAS,MAAM,KAAK,cAAc,SAAS,KAAK,GAAG;gBAC7D,UAAU,SAAS,MAAM,KAAK,cAAc,SAAS,KAAK,GAAG;gBAC7D,WAAW,UAAU,MAAM,KAAK,cAAc,UAAU,KAAK,GAAG;gBAChE,aAAa,YAAY,MAAM,KAAK,cAAc,YAAY,KAAK,GAAG;gBACtE,QAAQ;uBACF,SAAS,MAAM,KAAK,aAAa;wBAAC;4BAAE,SAAS;4BAAY,OAAO,SAAS,MAAM;wBAAC;qBAAE,GAAG,EAAE;uBACvF,SAAS,MAAM,KAAK,aAAa;wBAAC;4BAAE,SAAS;4BAAY,OAAO,SAAS,MAAM;wBAAC;qBAAE,GAAG,EAAE;uBACvF,UAAU,MAAM,KAAK,aAAa;wBAAC;4BAAE,SAAS;4BAAa,OAAO,UAAU,MAAM;wBAAC;qBAAE,GAAG,EAAE;uBAC1F,YAAY,MAAM,KAAK,aAAa;wBAAC;4BAAE,SAAS;4BAAe,OAAO,YAAY,MAAM;wBAAC;qBAAE,GAAG,EAAE;iBACrG;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1871, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/adolfo-lutz-backend/dashboard/src/app/patients/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { DashboardLayout } from \"@/components/dashboard/layout\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport {\n  Users,\n  UserPlus,\n  UserCheck,\n  TrendingUp,\n  Calendar,\n  MapPin,\n  Clock,\n  Filter,\n  Loader2,\n  AlertCircle\n} from \"lucide-react\";\nimport { PatientAgeChart } from \"@/components/charts/patient-age-chart\";\nimport { PatientGenderChart } from \"@/components/charts/patient-gender-chart\";\nimport { PatientLocationChart } from \"@/components/charts/patient-location-chart\";\nimport { PatientActivityChart } from \"@/components/charts/patient-activity-chart\";\nimport { patientApi, type PatientAnalytics } from \"@/lib/api\";\n\nexport default function PatientsPage() {\n  const [data, setData] = useState<PatientAnalytics | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    async function fetchPatientData() {\n      try {\n        setLoading(true);\n        const patientData = await patientApi.getAnalytics() as PatientAnalytics;\n        setData(patientData);\n        setError(null);\n      } catch (err) {\n        console.error('Failed to fetch patient data:', err);\n        setError('Failed to load patient analytics');\n      } finally {\n        setLoading(false);\n      }\n    }\n\n    fetchPatientData();\n  }, []);\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold tracking-tight\">Patient Analytics</h1>\n            <p className=\"text-muted-foreground\">\n              Comprehensive analysis of patient demographics and behavior\n            </p>\n          </div>\n          <div className=\"flex items-center gap-2\">\n            <Button variant=\"outline\" size=\"sm\">\n              <Filter className=\"mr-2 h-4 w-4\" />\n              Filter\n            </Button>\n            <Badge variant=\"outline\" className=\"text-sm\">\n              <Calendar className=\"mr-1 h-3 w-3\" />\n              Last 30 days\n            </Badge>\n          </div>\n        </div>\n\n        {/* Key Patient Metrics */}\n        {loading ? (\n          <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n            {[...Array(4)].map((_, i) => (\n              <Card key={i}>\n                <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                  <div className=\"h-4 w-24 bg-gray-200 rounded animate-pulse\" />\n                  <div className=\"h-4 w-4 bg-gray-200 rounded animate-pulse\" />\n                </CardHeader>\n                <CardContent>\n                  <div className=\"h-8 w-16 bg-gray-200 rounded animate-pulse mb-2\" />\n                  <div className=\"h-3 w-20 bg-gray-200 rounded animate-pulse\" />\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        ) : error ? (\n          <Card className=\"border-red-200\">\n            <CardContent className=\"pt-6\">\n              <div className=\"flex items-center gap-2 text-red-600\">\n                <AlertCircle className=\"h-4 w-4\" />\n                <span>{error}</span>\n              </div>\n            </CardContent>\n          </Card>\n        ) : (\n          <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Active Patients</CardTitle>\n                <Users className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">\n                  {data?.metrics?.activePatients?.toLocaleString() || '0'}\n                </div>\n                <p className=\"text-xs text-muted-foreground\">\n                  Last 30 days\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">New Patients</CardTitle>\n                <UserPlus className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">\n                  +{data?.metrics?.newPatients?.toLocaleString() || '0'}\n                </div>\n                <p className=\"text-xs text-muted-foreground\">\n                  Last 30 days\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Top Patients</CardTitle>\n                <UserCheck className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">\n                  {data?.topPatients?.length || '0'}\n                </div>\n                <p className=\"text-xs text-muted-foreground\">\n                  Frequent visitors\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Demographics</CardTitle>\n                <Clock className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">\n                  {data?.demographics?.age?.length || '0'}\n                </div>\n                <p className=\"text-xs text-muted-foreground\">\n                  Age groups tracked\n                </p>\n              </CardContent>\n            </Card>\n          </div>\n        )}\n\n        {/* Charts Grid */}\n        <div className=\"grid gap-6 md:grid-cols-2\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Age Distribution</CardTitle>\n              <CardDescription>Patient distribution by age groups</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <PatientAgeChart data={data?.demographics?.age} />\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle>Gender Distribution</CardTitle>\n              <CardDescription>Patient distribution by gender</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <PatientGenderChart data={data?.demographics?.gender} />\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle>Geographic Distribution</CardTitle>\n              <CardDescription>Top 10 cities by patient count</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <PatientLocationChart data={data?.demographics?.location} />\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle>Patient Activity</CardTitle>\n              <CardDescription>New vs returning patients over time</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <PatientActivityChart />\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Top Patients Table */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Top Patients by Visit Frequency</CardTitle>\n            <CardDescription>Patients with the highest number of service orders</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {[\n                { name: \"Maria Silva Santos\", cpf: \"***.***.***-12\", visits: 24, lastVisit: \"2 days ago\" },\n                { name: \"João Carlos Oliveira\", cpf: \"***.***.***-34\", visits: 19, lastVisit: \"1 week ago\" },\n                { name: \"Ana Paula Costa\", cpf: \"***.***.***-56\", visits: 17, lastVisit: \"3 days ago\" },\n                { name: \"Carlos Eduardo Lima\", cpf: \"***.***.***-78\", visits: 15, lastVisit: \"5 days ago\" },\n                { name: \"Fernanda Rodrigues\", cpf: \"***.***.***-90\", visits: 14, lastVisit: \"1 day ago\" },\n              ].map((patient, index) => (\n                <div key={index} className=\"flex items-center justify-between p-3 border rounded-lg\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n                      <span className=\"text-sm font-medium text-blue-600\">\n                        {patient.name.split(' ').map(n => n[0]).join('').slice(0, 2)}\n                      </span>\n                    </div>\n                    <div>\n                      <p className=\"font-medium\">{patient.name}</p>\n                      <p className=\"text-sm text-muted-foreground\">{patient.cpf}</p>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"font-medium\">{patient.visits} visits</p>\n                    <p className=\"text-sm text-muted-foreground\">Last: {patient.lastVisit}</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AACA;AACA;;;AAvBA;;;;;;;;;;;;AAyBe,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,eAAe;gBACb,IAAI;oBACF,WAAW;oBACX,MAAM,cAAc,MAAM,oHAAA,CAAA,aAAU,CAAC,YAAY;oBACjD,QAAQ;oBACR,SAAS;gBACX,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAC,iCAAiC;oBAC/C,SAAS;gBACX,SAAU;oBACR,WAAW;gBACb;YACF;YAEA;QACF;iCAAG,EAAE;IACL,qBACE,6LAAC,4IAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;sDACjC,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;gBAO1C,wBACC,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;2BAPR;;;;;;;;;2BAYb,sBACF,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;8CAAM;;;;;;;;;;;;;;;;;;;;;yCAKb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;sDACZ,MAAM,SAAS,gBAAgB,oBAAoB;;;;;;sDAEtD,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;;gDAAqB;gDAChC,MAAM,SAAS,aAAa,oBAAoB;;;;;;;sDAEpD,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;8CAEvB,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;sDACZ,MAAM,aAAa,UAAU;;;;;;sDAEhC,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;sDACZ,MAAM,cAAc,KAAK,UAAU;;;;;;sDAEtC,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;8BASrD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,0JAAA,CAAA,kBAAe;wCAAC,MAAM,MAAM,cAAc;;;;;;;;;;;;;;;;;sCAI/C,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,6JAAA,CAAA,qBAAkB;wCAAC,MAAM,MAAM,cAAc;;;;;;;;;;;;;;;;;sCAIlD,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,+JAAA,CAAA,uBAAoB;wCAAC,MAAM,MAAM,cAAc;;;;;;;;;;;;;;;;;sCAIpD,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,+JAAA,CAAA,uBAAoB;;;;;;;;;;;;;;;;;;;;;;8BAM3B,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;;8CACT,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,mIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAEnB,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAsB,KAAK;wCAAkB,QAAQ;wCAAI,WAAW;oCAAa;oCACzF;wCAAE,MAAM;wCAAwB,KAAK;wCAAkB,QAAQ;wCAAI,WAAW;oCAAa;oCAC3F;wCAAE,MAAM;wCAAmB,KAAK;wCAAkB,QAAQ;wCAAI,WAAW;oCAAa;oCACtF;wCAAE,MAAM;wCAAuB,KAAK;wCAAkB,QAAQ;wCAAI,WAAW;oCAAa;oCAC1F;wCAAE,MAAM;wCAAsB,KAAK;wCAAkB,QAAQ;wCAAI,WAAW;oCAAY;iCACzF,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEACb,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,KAAK,CAAC,GAAG;;;;;;;;;;;kEAG9D,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAe,QAAQ,IAAI;;;;;;0EACxC,6LAAC;gEAAE,WAAU;0EAAiC,QAAQ,GAAG;;;;;;;;;;;;;;;;;;0DAG7D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;;4DAAe,QAAQ,MAAM;4DAAC;;;;;;;kEAC3C,6LAAC;wDAAE,WAAU;;4DAAgC;4DAAO,QAAQ,SAAS;;;;;;;;;;;;;;uCAd/D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwB1B;GAxNwB;KAAA", "debugId": null}}]}