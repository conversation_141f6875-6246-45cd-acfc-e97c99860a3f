// Test simple database queries to verify table access
const BASE_URL = 'http://localhost:3001';

async function testSimpleQuery() {
  try {
    console.log('🔍 Testing simple patient count query...');
    
    const response = await fetch(`${BASE_URL}/api/test-simple`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          SELECT COUNT(DISTINCT OS.Paciente) as eligible_count
          FROM dado.ArqOrdemServico OS
          JOIN dado.ArqPaciente P ON OS.Paciente = P.ID
          JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID
          WHERE OS.Data >= '2025-06-26'
            AND PF.TelefoneNumero IS NOT NULL
            AND PF.TelefoneNumero != ''
        `
      })
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Simple query SUCCESS');
      console.log('   Result:', data);
    } else {
      console.log('❌ Simple query ERROR');
      console.log('   Error:', data.error);
    }
  } catch (error) {
    console.log('❌ Network error:', error.message);
  }
}

testSimpleQuery();
