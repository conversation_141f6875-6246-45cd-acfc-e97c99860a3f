import axios from 'axios';
import { logger } from '../utils/logger.js';
import { config } from '../config/environment.js';

export class WhatsAppService {
    constructor() {
        this.apiUrl = config.whatsapp.apiUrl;
        this.accessToken = config.whatsapp.accessToken;
        this.phoneNumberId = config.whatsapp.phoneNumberId;
        this.npsFormUrl = config.whatsapp.npsFormUrl;
        
        // Configurar axios com headers padrão
        this.client = axios.create({
            baseURL: this.apiUrl,
            headers: {
                'Authorization': `Bearer ${this.accessToken}`,
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });
    }

    async sendNpsMessage(patient) {
        try {
            const message = this.createNpsMessage(patient);
            
            const response = await this.client.post(`/${this.phoneNumberId}/messages`, message);
            
            logger.info(`WhatsApp message sent to ${patient.whatsapp} (OS: ${patient.codigoOs})`, {
                messageId: response.data.messages?.[0]?.id,
                status: response.status
            });
            
            return { 
                success: true, 
                patient,
                messageId: response.data.messages?.[0]?.id,
                response: response.data
            };
            
        } catch (error) {
            logger.error(`Failed to send WhatsApp to ${patient.whatsapp}:`, {
                error: error.message,
                status: error.response?.status,
                data: error.response?.data,
                patient: patient.codigoOs
            });
            
            return { 
                success: false, 
                patient, 
                error: error.message,
                errorCode: error.response?.status,
                errorData: error.response?.data
            };
        }
    }

    createNpsMessage(patient) {
        const npsUrl = `${this.npsFormUrl}?os=${patient.codigoOs}&nome=${encodeURIComponent(patient.nome)}`;
        
        // Mensagem de texto com botões interativos
        return {
            messaging_product: "whatsapp",
            to: patient.whatsapp,
            type: "interactive",
            interactive: {
                type: "button",
                header: {
                    type: "text",
                    text: "🏥 Pesquisa de Satisfação"
                },
                body: {
                    text: `Olá ${patient.nome}!\n\nEsperamos que você tenha tido uma boa experiência em nosso laboratório.\n\nGostaríamos de saber sua opinião sobre o atendimento que você recebeu (OS: ${patient.codigoOs}).\n\n*Em uma escala de 0 a 10, o quanto você recomendaria nosso laboratório para um amigo ou familiar?*`
                },
                footer: {
                    text: "Sua opinião é muito importante para nós! 💙"
                },
                action: {
                    buttons: [
                        {
                            type: "reply",
                            reply: {
                                id: "nps_survey",
                                title: "📝 Responder Pesquisa"
                            }
                        },
                        {
                            type: "reply",
                            reply: {
                                id: "nps_later",
                                title: "⏰ Responder Depois"
                            }
                        }
                    ]
                }
            }
        };
    }

    // Mensagem de texto simples (fallback)
    createSimpleNpsMessage(patient) {
        const npsUrl = `${this.npsFormUrl}?os=${patient.codigoOs}&nome=${encodeURIComponent(patient.nome)}`;
        
        return {
            messaging_product: "whatsapp",
            to: patient.whatsapp,
            type: "text",
            text: {
                body: `🏥 *Pesquisa de Satisfação*\n\nOlá ${patient.nome}!\n\nEsperamos que você tenha tido uma boa experiência em nosso laboratório.\n\nGostaríamos de saber sua opinião sobre o atendimento que você recebeu (OS: ${patient.codigoOs}).\n\n*Em uma escala de 0 a 10, o quanto você recomendaria nosso laboratório para um amigo ou familiar?*\n\nResponda nossa pesquisa: ${npsUrl}\n\nSua opinião é muito importante para nós! 💙`
            }
        };
    }

    // Enviar template aprovado (se disponível)
    async sendTemplateMessage(patient, templateName = 'nps_survey') {
        try {
            const message = {
                messaging_product: "whatsapp",
                to: patient.whatsapp,
                type: "template",
                template: {
                    name: templateName,
                    language: {
                        code: "pt_BR"
                    },
                    components: [
                        {
                            type: "body",
                            parameters: [
                                {
                                    type: "text",
                                    text: patient.nome
                                },
                                {
                                    type: "text",
                                    text: patient.codigoOs
                                }
                            ]
                        },
                        {
                            type: "button",
                            sub_type: "url",
                            index: "0",
                            parameters: [
                                {
                                    type: "text",
                                    text: `os=${patient.codigoOs}&nome=${encodeURIComponent(patient.nome)}`
                                }
                            ]
                        }
                    ]
                }
            };

            const response = await this.client.post(`/${this.phoneNumberId}/messages`, message);
            
            logger.info(`WhatsApp template sent to ${patient.whatsapp} (OS: ${patient.codigoOs})`, {
                messageId: response.data.messages?.[0]?.id,
                template: templateName
            });
            
            return { 
                success: true, 
                patient,
                messageId: response.data.messages?.[0]?.id,
                template: templateName
            };
            
        } catch (error) {
            logger.warn(`Template message failed, falling back to interactive message`, {
                error: error.message,
                patient: patient.codigoOs
            });
            
            // Fallback para mensagem interativa
            return await this.sendNpsMessage(patient);
        }
    }

    async sendBatchMessages(patients, useTemplate = false) {
        const results = await Promise.allSettled(
            patients.map(patient => 
                useTemplate 
                    ? this.sendTemplateMessage(patient)
                    : this.sendNpsMessage(patient)
            )
        );

        // Marcar pacientes com status de envio
        patients.forEach((patient, index) => {
            const result = results[index];
            patient.messageSent = result.status === 'fulfilled' && result.value.success;
            patient.messageId = result.value?.messageId;
            patient.error = result.value?.error || result.reason;
        });

        const summary = {
            total: patients.length,
            sent: results.filter(r => r.status === 'fulfilled' && r.value.success).length,
            failed: results.filter(r => r.status === 'rejected' || !r.value.success).length,
            details: results
        };

        logger.info(`WhatsApp batch complete: ${summary.sent} sent, ${summary.failed} failed`);
        return summary;
    }

    // Verificar status de uma mensagem
    async getMessageStatus(messageId) {
        try {
            const response = await this.client.get(`/${messageId}`);
            return response.data;
        } catch (error) {
            logger.error(`Failed to get message status for ${messageId}:`, error.message);
            return null;
        }
    }

    // Webhook para receber status de entrega e respostas
    handleWebhook(webhookData) {
        try {
            const entry = webhookData.entry?.[0];
            const changes = entry?.changes?.[0];
            const value = changes?.value;

            if (value?.statuses) {
                // Status de entrega
                value.statuses.forEach(status => {
                    logger.info('WhatsApp message status update:', {
                        messageId: status.id,
                        status: status.status,
                        timestamp: status.timestamp,
                        recipient: status.recipient_id
                    });
                });
            }

            if (value?.messages) {
                // Respostas dos usuários
                value.messages.forEach(message => {
                    logger.info('WhatsApp message received:', {
                        from: message.from,
                        type: message.type,
                        text: message.text?.body,
                        timestamp: message.timestamp
                    });
                    
                    // Aqui você pode processar as respostas do NPS
                    this.processNpsResponse(message);
                });
            }

            return { success: true };
        } catch (error) {
            logger.error('Error processing WhatsApp webhook:', error);
            return { success: false, error: error.message };
        }
    }

    processNpsResponse(message) {
        // Processar resposta do NPS
        if (message.type === 'interactive' && message.interactive?.button_reply?.id === 'nps_survey') {
            logger.info('User clicked NPS survey button:', {
                from: message.from,
                timestamp: message.timestamp
            });
            
            // Aqui você pode enviar o link da pesquisa ou continuar o fluxo
        }
        
        // Processar respostas de texto que podem ser notas do NPS
        if (message.type === 'text') {
            const text = message.text.body.trim();
            const npsScore = parseInt(text);
            
            if (!isNaN(npsScore) && npsScore >= 0 && npsScore <= 10) {
                logger.info('NPS score received:', {
                    from: message.from,
                    score: npsScore,
                    timestamp: message.timestamp
                });
                
                // Salvar score no banco de dados
                // this.saveNpsScore(message.from, npsScore);
            }
        }
    }
}
