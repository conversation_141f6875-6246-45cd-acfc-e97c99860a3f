import { getConnection, closeConnection } from '../config/database.js';
import { logger } from '../utils/logger.js';

const PATIENT_QUERY = `
    SELECT
        OS.ID,
        OS.CodigoOs,
        OS.Data,
        OS.HoraInicial,
        OS.Paciente,
        PF.Nome,
        PF.Email,
        PF.TelefoneDdd,
        PF.TelefoneNumero
    FROM dado.ArqOrdemServico OS
    JOIN dado.ArqPaciente P ON OS.Paciente = P.ID
    JOIN dado.TblPessoaFisica PF ON P.PessoaFisica = PF.ID
    WHERE OS.Data >= DATEADD('day', -1, CURRENT_TIMESTAMP)
        AND PF.TelefoneNumero IS NOT NULL
        AND PF.TelefoneNumero != ''
    ORDER BY OS.Data DESC, OS.HoraInicial DESC
`;

export async function getNewPatients() {
    let connection = null;
    
    try {
        connection = await getConnection();
        const results = await connection.query(PATIENT_QUERY);
        
        // Converter BigInt para string e validar telefones
        const patients = results.map(row => {
            // Construir telefone completo se tiver DDD e número
            const telefoneCompleto = row.TelefoneDdd && row.TelefoneNumero
                ? `${row.TelefoneDdd}${row.TelefoneNumero}`
                : null;

            return {
                id: row.ID?.toString(),
                codigoOs: row.CodigoOs,
                data: row.Data,
                horaInicial: row.HoraInicial,
                pacienteId: row.Paciente?.toString(),
                nome: row.Nome,
                email: row.Email?.trim().toLowerCase(),
                telefone: telefoneCompleto?.trim(),
                whatsapp: getWhatsAppNumber(telefoneCompleto)
            };
        }).filter(patient => patient.whatsapp);
        
        logger.info(`Found ${patients.length} patients with valid WhatsApp numbers`);
        return patients;
        
    } catch (error) {
        logger.error('Error fetching patients:', error);
        throw error;
    } finally {
        await closeConnection(connection);
    }
}

function getWhatsAppNumber(telefone) {
    if (!telefone) return null;

    // Limpar número (remover caracteres especiais)
    const cleanNumber = telefone.replace(/\D/g, '');

    // Verificar se tem pelo menos 10 dígitos (formato brasileiro)
    if (cleanNumber.length < 10) return null;

    // Adicionar código do país se não tiver (55 para Brasil)
    let formattedNumber = cleanNumber;
    if (!formattedNumber.startsWith('55') && formattedNumber.length <= 11) {
        formattedNumber = '55' + formattedNumber;
    }

    // Verificar se é um número válido (11 ou 13 dígitos com código do país)
    if (formattedNumber.length === 13 || formattedNumber.length === 12) {
        return formattedNumber;
    }

    return null;
}
