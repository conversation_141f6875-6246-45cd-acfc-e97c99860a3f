import { Resend } from 'resend';
import { createObjectCsvWriter } from 'csv-writer';
import fs from 'fs';
import path from 'path';
import { logger } from '../utils/logger.js';

export class EmailService {
    constructor() {
        this.resend = new Resend(process.env.RESEND_API_KEY);
        this.fromEmail = process.env.EMAIL_FROM || '<EMAIL>';
        this.toEmail = process.env.EMAIL_TO || '<EMAIL>';
    }

    async sendNpsReport(patients, stats) {
        try {
            logger.info('Generating NPS report...');

            // Gerar arquivo CSV
            const csvPath = await this.generateCsvReport(patients);
            
            // Ler o arquivo CSV
            const csvContent = fs.readFileSync(csvPath);
            const csvBase64 = csvContent.toString('base64');

            // Formatar data e hora
            const now = new Date();
            const dateStr = now.toLocaleDateString('pt-BR');
            const timeStr = now.toLocaleTimeString('pt-BR');

            // Criar conteúdo do email
            const emailContent = this.generateEmailContent(stats, dateStr, timeStr);

            // Enviar email com anexo
            const response = await this.resend.emails.send({
                from: this.fromEmail,
                to: this.toEmail,
                subject: `Relatório NPS - ${dateStr}`,
                html: emailContent,
                attachments: [
                    {
                        filename: `nps_report_${now.toISOString().split('T')[0]}.csv`,
                        content: csvBase64
                    }
                ]
            });

            // Limpar arquivo temporário
            fs.unlinkSync(csvPath);

            logger.info('NPS report sent successfully', { emailId: response.data?.id });
            return response;

        } catch (error) {
            logger.error('Failed to send NPS report:', error);
            throw error;
        }
    }

    async generateCsvReport(patients) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `nps_report_${timestamp}.csv`;
        const csvPath = path.join(process.cwd(), 'temp', filename);

        // Criar diretório temp se não existir
        const tempDir = path.join(process.cwd(), 'temp');
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }

        // Configurar escritor CSV
        const csvWriter = createObjectCsvWriter({
            path: csvPath,
            header: [
                { id: 'patientId', title: 'ID do Paciente' },
                { id: 'name', title: 'Nome' },
                { id: 'phone', title: 'Telefone' },
                { id: 'visitDate', title: 'Data da Visita' },
                { id: 'status', title: 'Status do Envio' },
                { id: 'sentAt', title: 'Enviado em' }
            ]
        });

        // Preparar dados para o CSV
        const records = patients.map(patient => ({
            patientId: patient.id,
            name: patient.name,
            phone: patient.phone,
            visitDate: new Date(patient.visitDate).toLocaleDateString('pt-BR'),
            status: patient.messageSent ? 'Enviado' : 'Falhou',
            sentAt: patient.messageSent ? new Date().toLocaleString('pt-BR') : 'N/A'
        }));

        // Escrever arquivo CSV
        await csvWriter.writeRecords(records);
        logger.info(`CSV report generated: ${csvPath}`);

        return csvPath;
    }

    generateEmailContent(stats, dateStr, timeStr) {
        const successRate = ((stats.sent / stats.total) * 100).toFixed(1);
        
        return `
<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #0066cc;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            background-color: #f9f9f9;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 0 0 5px 5px;
        }
        .stats {
            background-color: white;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            border: 1px solid #e0e0e0;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .stat-item:last-child {
            border-bottom: none;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Relatório de Envio NPS</h1>
            <p>${dateStr} às ${timeStr}</p>
        </div>
        
        <div class="content">
            <h2>Resumo do Envio</h2>
            <p>O processo de envio de pesquisas NPS foi concluído com os seguintes resultados:</p>
            
            <div class="stats">
                <div class="stat-item">
                    <span>Total de pacientes elegíveis:</span>
                    <span><strong>${stats.total}</strong></span>
                </div>
                <div class="stat-item">
                    <span>Mensagens enviadas com sucesso:</span>
                    <span class="success">${stats.sent}</span>
                </div>
                <div class="stat-item">
                    <span>Falhas no envio:</span>
                    <span class="${stats.failed > 0 ? 'error' : 'success'}">${stats.failed}</span>
                </div>
                <div class="stat-item">
                    <span>Taxa de sucesso:</span>
                    <span class="${successRate >= 90 ? 'success' : successRate >= 70 ? 'warning' : 'error'}">${successRate}%</span>
                </div>
                <div class="stat-item">
                    <span>Tempo de processamento:</span>
                    <span>${(stats.duration / 1000).toFixed(2)} segundos</span>
                </div>
            </div>
            
            <h3>Detalhes</h3>
            <p>Em anexo você encontrará o arquivo CSV com a lista completa de pacientes e o status de envio de cada mensagem.</p>
            
            ${stats.failed > 0 ? `
            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin-top: 15px;">
                <strong>⚠️ Atenção:</strong> Houve ${stats.failed} falha(s) no envio. Verifique o arquivo CSV para mais detalhes.
            </div>
            ` : ''}
            
            <h3>Informações Importantes</h3>
            <ul>
                <li>As mensagens foram enviadas via WhatsApp Business API</li>
                <li>O link do formulário NPS utilizado: ${process.env.NPS_FORM_URL || 'Não configurado'}</li>
                <li>Os pacientes elegíveis são aqueles que visitaram a clínica nos últimos 7 dias</li>
                <li>Cada paciente recebe apenas uma pesquisa NPS</li>
            </ul>
        </div>
        
        <div class="footer">
            <p>Este é um email automático gerado pelo sistema de NPS do Instituto Adolfo Lutz.</p>
            <p>Em caso de dúvidas, entre em contato com o suporte técnico.</p>
        </div>
    </div>
</body>
</html>
        `;
    }
} 