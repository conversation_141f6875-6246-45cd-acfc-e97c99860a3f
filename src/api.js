import express from 'express';
import { getNewPatients } from './services/patientService.js';
import { logger } from './utils/logger.js';
import { config } from './config/environment.js';

const app = express();
const port = process.env.API_PORT || 3000;

// Middleware
app.use(express.json());

// Request logging middleware
app.use((req, res, next) => {
    logger.info(`${req.method} ${req.url}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
    });
    next();
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'NPS WhatsApp API',
        timestamp: new Date().toISOString(),
        environment: config.nodeEnv
    });
});

// Get recent patients endpoint
app.get('/api/patients/recent', async (req, res) => {
    const startTime = Date.now();

    try {
        logger.info('Manual patient query requested');

        // Get patients from database
        const patients = await getNewPatients();

        const duration = Date.now() - startTime;

        // Response with metadata
        const response = {
            success: true,
            timestamp: new Date().toISOString(),
            query: {
                period: 'last 24 hours',
                duration: `${duration}ms`,
                source: 'database'
            },
            data: {
                total: patients.length,
                withValidWhatsApp: patients.length,
                patients: patients
            }
        };

        logger.info(`Patient query completed in ${duration}ms`, {
            totalFound: patients.length,
            withWhatsApp: patients.length,
            duration,
            source: 'database'
        });

        res.json(response);

    } catch (error) {
        const duration = Date.now() - startTime;

        logger.error(`Patient query failed after ${duration}ms:`, error);

        res.status(500).json({
            success: false,
            error: 'Failed to retrieve patients',
            message: error.message,
            timestamp: new Date().toISOString(),
            duration: `${duration}ms`
        });
    }
});

// Configuration info endpoint
app.get('/api/config', (req, res) => {
    res.json({
        service: 'NPS WhatsApp API',
        mode: 'API Server',
        environment: config.nodeEnv,
        database: {
            host: config.database.host,
            namespace: config.database.namespace,
            connected: true // Could add actual connection test
        },
        whatsapp: {
            configured: !!(config.whatsapp.accessToken && config.whatsapp.phoneNumberId),
            phoneNumberId: config.whatsapp.phoneNumberId ? 'configured' : 'missing'
        },
        nps: {
            formUrl: config.nps.formUrl
        },
        timestamp: new Date().toISOString()
    });
});



// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Endpoint not found',
        path: req.originalUrl,
        availableEndpoints: [
            'GET /health',
            'GET /api/patients/recent',
            'GET /api/config'
        ],
        timestamp: new Date().toISOString()
    });
});

// Error handling middleware
app.use((error, req, res, next) => {
    logger.error('API Error:', error);
    
    res.status(500).json({
        error: 'Internal server error',
        message: error.message,
        timestamp: new Date().toISOString()
    });
});





// Graceful shutdown
process.on('SIGTERM', () => {
    logger.info('SIGTERM received, shutting down API server gracefully');
    server.close(() => {
        logger.info('API server closed');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    logger.info('SIGINT received, shutting down API server gracefully');
    server.close(() => {
        logger.info('API server closed');
        process.exit(0);
    });
});

// Start server
const server = app.listen(port, () => {
    logger.info(`NPS WhatsApp API Server running on port ${port}`);
    logger.info(`Environment: ${config.nodeEnv}`);
    logger.info('Available endpoints:');
    logger.info('  GET /health - Health check');
    logger.info('  GET /api/patients/recent - Get patients from last 24h');
    logger.info('  GET /api/config - Configuration info');
});
