import * as odbc from 'odbc';
import { logger } from '../utils/logger.js';

// Build connection string from environment variables
function buildConnectionString() {
    const driverPath = process.env.IRIS_DRIVER_PATH;
    
    // If no driver path is specified or is "system", use system driver name
    const driverPart = (!driverPath || driverPath === 'system') 
        ? 'DRIVER={InterSystems ODBC35}'
        : `DRIVER=${driverPath}`;

    return `${driverPart};` +
           `SERVER=${process.env.IRIS_HOST};` +
           `PORT=${process.env.IRIS_PORT || 1972};` +
           `DATABASE=${process.env.IRIS_NAMESPACE};` +
           `UID=${process.env.IRIS_USER};` +
           `PWD=${process.env.IRIS_PASSWORD};` +
           `SSL_MODE=${process.env.IRIS_SSL_MODE || 0};`;
}

export async function getConnection() {
    try {
        const connectionString = buildConnectionString();
        
        const connection = await odbc.connect({
            connectionString,
            connectionTimeout: 30,
            loginTimeout: 30,
        });
        logger.info('Database connection established');
        return connection;
    } catch (error) {
        logger.error('Database connection failed:', error);
        throw error;
    }
}

export async function closeConnection(connection) {
    if (connection) {
        try {
            await connection.close();
            logger.info('Database connection closed');
        } catch (error) {
            logger.error('Error closing connection:', error);
        }
    }
}
