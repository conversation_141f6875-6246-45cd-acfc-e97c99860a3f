import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export const config = {
  // Node Environment
  nodeEnv: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT) || 3000,
  logLevel: process.env.LOG_LEVEL || 'info',

  // Database Configuration
  database: {
    host: process.env.IRIS_HOST,
    port: parseInt(process.env.IRIS_PORT) || 1972,
    namespace: process.env.IRIS_NAMESPACE || 'USER',
    user: process.env.IRIS_USER,
    password: process.env.IRIS_PASSWORD,
    sslMode: parseInt(process.env.IRIS_SSL_MODE) || 0,
  },

  // WhatsApp Configuration
  whatsapp: {
    apiUrl: process.env.WHATSAPP_API_URL || 'https://graph.facebook.com/v18.0',
    accessToken: process.env.WHATSAPP_ACCESS_TOKEN,
    phoneNumberId: process.env.WHATSAPP_PHONE_NUMBER_ID,
    webhookVerifyToken: process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN,
  },

  // NPS Configuration
  nps: {
    formUrl: process.env.NPS_FORM_URL || 'https://forms.laboratorio.com.br/nps'
  },



  // Job Configuration
  job: {
    cronSchedule: process.env.CRON_SCHEDULE || '0 10 * * *',
    runOnStartup: process.env.RUN_ON_STARTUP === 'true',
  },

};

// Validate required environment variables
const requiredVars = [
  'IRIS_HOST',
  'IRIS_USER',
  'IRIS_PASSWORD',
  'WHATSAPP_ACCESS_TOKEN',
  'WHATSAPP_PHONE_NUMBER_ID',
  'NPS_FORM_URL'
];

const missingVars = requiredVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('Missing required environment variables:', missingVars);
  if (process.env.NODE_ENV === 'production') {
    process.exit(1);
  }
}
