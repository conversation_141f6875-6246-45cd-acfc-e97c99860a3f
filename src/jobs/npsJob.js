import cron from 'node-cron';
import { getNewPatients } from '../services/patientService.js';
import { WhatsAppService } from '../services/whatsappService.js';
import { EmailService } from '../services/emailService.js';
import { logger } from '../utils/logger.js';

export class NpsJob {
    constructor() {
        this.whatsappService = new WhatsAppService();
        this.emailService = new EmailService();
        this.isRunning = false;
        this.task = null;
    }

    async execute() {
        if (this.isRunning) {
            logger.warn('NPS job already running, skipping...');
            return;
        }

        this.isRunning = true;
        const startTime = Date.now();

        try {
            logger.info('Starting NPS survey processing');

            // Buscar pacientes elegíveis
            const patients = await getNewPatients();

            if (patients.length === 0) {
                logger.info('No eligible patients found for NPS surveys');
                const duration = Date.now() - startTime;
                logger.info(`NPS job completed in ${duration}ms - No patients to process`);
                return;
            }

            logger.info(`Found ${patients.length} eligible patients for NPS surveys`);

            // Enviar mensagens WhatsApp em lote
            const whatsappResults = await this.whatsappService.sendBatchMessages(patients);

            // Calcular métricas finais
            const duration = Date.now() - startTime;
            const stats = {
                total: whatsappResults.total,
                sent: whatsappResults.sent,
                failed: whatsappResults.failed,
                duration
            };

            logger.info(`NPS survey processing completed in ${duration}ms`, {
                totalPatients: stats.total,
                messagesSent: stats.sent,
                messagesFailed: stats.failed,
                successRate: `${((stats.sent / stats.total) * 100).toFixed(1)}%`
            });

            // Enviar relatório por email
            try {
                await this.emailService.sendNpsReport(patients, stats);
                logger.info('Email report sent successfully');
            } catch (emailError) {
                logger.error('Failed to send email report:', emailError);
                // Não falhar o job inteiro se o email falhar
            }

            return stats;

        } catch (error) {
            const duration = Date.now() - startTime;
            logger.error(`NPS job failed after ${duration}ms:`, error);
            throw error;
        } finally {
            this.isRunning = false;
        }
    }

    start() {
        // Validar configuração antes de iniciar
        const validation = this.validateConfiguration();
        if (!validation.valid) {
            logger.error('Cannot start NPS job due to configuration errors:', validation.errors);
            return false;
        }

        const cronExpression = process.env.CRON_SCHEDULE || '0 10 * * *'; // Default: 10:00 AM daily

        logger.info(`Scheduling NPS job with cron: ${cronExpression}`);

        this.task = cron.schedule(cronExpression, async () => {
            await this.execute();
        });

        // Executar imediatamente se configurado
        if (process.env.RUN_ON_STARTUP === 'true') {
            logger.info('RUN_ON_STARTUP enabled, executing job immediately...');
            this.execute();
        }

        return true;
    }

    stop() {
        if (this.task) {
            this.task.stop();
            logger.info('NPS job stopped');
        }
    }

    validateConfiguration() {
        const errors = [];

        // Verificar configurações de WhatsApp
        if (!process.env.WHATSAPP_ACCESS_TOKEN) {
            errors.push('WHATSAPP_ACCESS_TOKEN not configured');
        }
        if (!process.env.WHATSAPP_PHONE_NUMBER_ID) {
            errors.push('WHATSAPP_PHONE_NUMBER_ID not configured');
        }
        if (!process.env.NPS_FORM_URL) {
            errors.push('NPS_FORM_URL not configured');
        }

        // Verificar configurações de banco
        if (!process.env.IRIS_HOST) {
            errors.push('IRIS_HOST not configured');
        }
        if (!process.env.IRIS_USER) {
            errors.push('IRIS_USER not configured');
        }
        if (!process.env.IRIS_PASSWORD) {
            errors.push('IRIS_PASSWORD not configured');
        }

        // Verificar configurações de email
        if (!process.env.RESEND_API_KEY) {
            errors.push('RESEND_API_KEY not configured');
        }
        if (!process.env.EMAIL_TO) {
            errors.push('EMAIL_TO not configured (recipient email)');
        }

        const isValid = errors.length === 0;

        if (isValid) {
            logger.info('Configuration validation passed');
        } else {
            logger.error('Configuration validation failed:', errors);
        }

        return {
            valid: isValid,
            errors
        };
    }
}
