import { NpsJob } from './jobs/npsJob.js';
import { logger } from './utils/logger.js';
import { config } from './config/environment.js';

// Initialize and start NPS job
logger.info('Starting NPS WhatsApp Service...');
logger.info(`Environment: ${config.nodeEnv}`);

const npsJob = new NpsJob();
const started = npsJob.start();

if (started) {
    logger.info('NPS job scheduler started successfully');
    logger.info(`Cron schedule: ${config.job.cronSchedule}`);
} else {
    logger.error('Failed to start NPS job due to configuration errors');
    process.exit(1);
}

// Graceful shutdown
process.on('SIGTERM', () => {
    logger.info('SIGTERM received, shutting down gracefully');
    npsJob.stop();
    process.exit(0);
});

process.on('SIGINT', () => {
    logger.info('SIGINT received, shutting down gracefully');
    npsJob.stop();
    process.exit(0);
});