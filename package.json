{"name": "nps-whatsapp-service", "version": "1.0.0", "description": "Simple NPS WhatsApp Service for Laboratory", "main": "src/server.js", "type": "module", "engines": {"node": ">=20.0.0"}, "scripts": {"start": "node src/server.js", "cron": "node src/server.js", "api": "node src/api.js", "dev:cron": "node --watch src/server.js", "dev:api": "node --watch src/api.js"}, "dependencies": {"axios": "^1.10.0", "csv-writer": "^1.6.0", "dotenv": "^16.4.7", "express": "^4.21.2", "node-cron": "^3.0.3", "odbc": "^2.4.9", "resend": "^4.6.0", "winston": "^3.11.0"}}