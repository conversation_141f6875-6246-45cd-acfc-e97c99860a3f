import { WhatsAppService } from './src/services/whatsappService.js';
import { EmailService } from './src/services/emailService.js';
import dotenv from 'dotenv';
import { logger } from './src/utils/logger.js';

// Carregar variáveis de ambiente
dotenv.config();

async function testE2ENps() {
    console.log('🚀 Iniciando teste E2E - Disparo NPS + Relatório por Email\n');

    // Configurações do teste
    const testPhone = '+351969174239';
    
    // Criar paciente fictício para o teste
    const testPatient = {
        id: 'TEST001',
        codigoOs: `TEST${Date.now()}`,
        data: new Date(),
        horaInicial: new Date().toLocaleTimeString('pt-BR'),
        pacienteId: 'PAC001',
        nome: '<PERSON> (TESTE)',
        email: '<EMAIL>',
        telefone: testPhone.replace('+', ''),
        whatsapp: testPhone.replace('+', '') // Remove o + para formato interno
    };

    console.log('📋 Dados do teste:');
    console.log(`- Paciente: ${testPatient.nome}`);
    console.log(`- OS: ${testPatient.codigoOs}`);
    console.log(`- WhatsApp: ${testPhone}`);
    console.log(`- Email relatório: ${process.env.EMAIL_TO || 'não configurado'}\n`);

    // Validar configurações
    console.log('🔍 Validando configurações...');
    
    const requiredEnvVars = [
        'WHATSAPP_ACCESS_TOKEN',
        'WHATSAPP_PHONE_NUMBER_ID',
        'NPS_FORM_URL',
        'RESEND_API_KEY',
        'EMAIL_TO'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
        console.error('❌ Variáveis de ambiente faltando:', missingVars.join(', '));
        console.log('\n💡 Configure as seguintes variáveis no .env:');
        missingVars.forEach(varName => {
            console.log(`- ${varName}`);
        });
        return;
    }

    console.log('✅ Todas as configurações necessárias estão presentes\n');

    try {
        // ===== ETAPA 1: ENVIAR MENSAGEM WHATSAPP =====
        console.log('📱 ETAPA 1: Enviando mensagem NPS via WhatsApp...');
        
        const whatsappService = new WhatsAppService();
        const startTime = Date.now();
        
        console.log(`- Enviando para: ${testPhone}`);
        console.log(`- Paciente: ${testPatient.nome}`);
        console.log(`- OS: ${testPatient.codigoOs}`);
        
        const whatsappResult = await whatsappService.sendNpsMessage(testPatient);
        const whatsappDuration = Date.now() - startTime;
        
        if (whatsappResult.success) {
            console.log('✅ Mensagem WhatsApp enviada com sucesso!');
            console.log(`- Message ID: ${whatsappResult.messageId}`);
            console.log(`- Tempo: ${whatsappDuration}ms`);
            
            // Marcar o paciente como enviado com sucesso
            testPatient.messageSent = true;
            testPatient.messageId = whatsappResult.messageId;
            testPatient.sentAt = new Date();
        } else {
            console.log('❌ Falha no envio da mensagem WhatsApp');
            console.log(`- Erro: ${whatsappResult.error}`);
            console.log(`- Código: ${whatsappResult.errorCode}`);
            
            // Marcar o paciente como falha
            testPatient.messageSent = false;
            testPatient.error = whatsappResult.error;
        }

        console.log('\n');

        // ===== ETAPA 2: PREPARAR DADOS PARA RELATÓRIO =====
        console.log('📊 ETAPA 2: Preparando dados para relatório...');
        
        // Adaptar dados do paciente para o formato esperado pelo EmailService
        const patientsForReport = [{
            id: testPatient.id,
            name: testPatient.nome,
            phone: testPhone,
            visitDate: testPatient.data,
            messageSent: testPatient.messageSent,
            messageId: testPatient.messageId,
            error: testPatient.error || null,
            sentAt: testPatient.sentAt
        }];

        // Estatísticas do teste
        const stats = {
            total: 1,
            sent: testPatient.messageSent ? 1 : 0,
            failed: testPatient.messageSent ? 0 : 1,
            duration: whatsappDuration
        };

        console.log(`- Total de pacientes: ${stats.total}`);
        console.log(`- Enviados: ${stats.sent}`);
        console.log(`- Falhas: ${stats.failed}`);
        console.log(`- Taxa de sucesso: ${((stats.sent / stats.total) * 100).toFixed(1)}%`);

        console.log('\n');

        // ===== ETAPA 3: ENVIAR RELATÓRIO POR EMAIL =====
        console.log('📧 ETAPA 3: Enviando relatório por email...');
        
        const emailService = new EmailService();
        
        console.log(`- De: ${process.env.EMAIL_FROM || '<EMAIL>'}`);
        console.log(`- Para: ${process.env.EMAIL_TO}`);
        console.log('- Anexo: CSV com dados do teste');
        
        const emailStartTime = Date.now();
        const emailResult = await emailService.sendNpsReport(patientsForReport, stats);
        const emailDuration = Date.now() - emailStartTime;
        
        console.log('✅ Relatório enviado por email com sucesso!');
        console.log(`- Email ID: ${emailResult.data?.id || 'N/A'}`);
        console.log(`- Tempo: ${emailDuration}ms`);

        console.log('\n');

        // ===== RESUMO FINAL =====
        console.log('🎉 TESTE E2E CONCLUÍDO COM SUCESSO!');
        console.log('\n📋 Resumo do teste:');
        console.log(`- WhatsApp: ${testPatient.messageSent ? '✅ Enviado' : '❌ Falhou'}`);
        console.log(`- Email: ✅ Enviado`);
        console.log(`- Tempo total: ${Date.now() - startTime}ms`);
        
        console.log('\n📱 Próximos passos:');
        console.log('1. Verifique se a mensagem chegou no WhatsApp');
        console.log('2. Verifique o email com o relatório anexado');
        console.log('3. Teste o link da pesquisa NPS na mensagem');
        
        if (testPatient.messageSent) {
            console.log(`\n🔗 Link da pesquisa NPS:`);
            console.log(`${process.env.NPS_FORM_URL}?os=${testPatient.codigoOs}&nome=${encodeURIComponent(testPatient.nome)}`);
        }

    } catch (error) {
        console.error('\n❌ ERRO NO TESTE E2E:', error.message);
        
        if (error.response?.data) {
            console.error('\nDetalhes do erro:');
            console.error(JSON.stringify(error.response.data, null, 2));
        }

        console.log('\n💡 Dicas para solução:');
        console.log('1. Verifique se todas as variáveis de ambiente estão corretas');
        console.log('2. Confirme se o token do WhatsApp tem permissões adequadas');
        console.log('3. Verifique se o número de telefone está no formato correto');
        console.log('4. Confirme se a API key do Resend está válida');
        console.log('5. Verifique se o número do WhatsApp está registrado na API');
        
        // Log detalhado para debug
        logger.error('E2E Test failed:', {
            error: error.message,
            stack: error.stack,
            testPatient,
            timestamp: new Date().toISOString()
        });
    }
}

// Executar teste
console.log('🧪 TESTE END-TO-END: NPS WhatsApp + Email Report');
console.log('=' .repeat(50));
testE2ENps().catch(console.error); 