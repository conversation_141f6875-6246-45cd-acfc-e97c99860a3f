# 📱 NPS WhatsApp Service

Serviço automatizado para envio de pesquisas NPS via WhatsApp Business API para pacientes de laboratório.

## ✨ Funcionalidades

- **Execução Automatizada**: Job agendado via cron para execução diária
- **Integração com IRIS**: Consulta pacientes no InterSystems IRIS Data Platform
- **Envio via WhatsApp**: Mensagens NPS via WhatsApp Business API
- **Relatório por Email**: Envio automático de relatório com CSV após cada execução
- **API REST**: Endpoints para consultas manuais e integração

## 🚀 Início Rápido

### 1. Instalação
```bash
git clone <repository-url>
cd nps-whatsapp-service
npm install
```

### 2. Configuração
```bash
cp .env.example .env
# Edite o .env com suas configurações
```

### 3. Execução
```bash
# Modo automático (produção)
npm start

# Modo API (desenvolvimento)
npm run api
```

## ⚙️ Configuração

### Variáveis de Ambiente Obrigatórias

```bash
# Database
IRIS_HOST=your-iris-host
IRIS_USER=your-user
IRIS_PASSWORD=your-password
IRIS_NAMESPACE=your-namespace

# WhatsApp Business API
WHATSAPP_ACCESS_TOKEN=your-access-token
WHATSAPP_PHONE_NUMBER_ID=your-phone-number-id
NPS_FORM_URL=https://your-nps-form.com

# Email (Resend)
RESEND_API_KEY=your-resend-api-key
EMAIL_TO=<EMAIL>  # Email que receberá os relatórios

# Opcional
CRON_SCHEDULE=0 10 * * *  # Diário às 10:00 AM
EMAIL_FROM=<EMAIL>  # Email remetente (opcional)
```

## 📱 Configuração do WhatsApp Business API

### 1. Criar WhatsApp Business Account
1. Acesse [Facebook Business](https://business.facebook.com)
2. Crie uma conta business
3. Adicione WhatsApp Business API

### 2. Obter Credenciais
1. **Access Token**: No painel do Facebook Developers
2. **Phone Number ID**: No console do WhatsApp Business

### 3. Configurar Webhook (Opcional)
```bash
# URL do webhook para confirmações
WHATSAPP_WEBHOOK_URL=https://your-domain.com/webhook
WHATSAPP_VERIFY_TOKEN=your-verify-token
```

## 🔄 Modos de Operação

### 🤖 Modo Automático (Produção)
```bash
npm start
```
- Executa automaticamente via cron
- Ideal para produção

### 🌐 Modo API (Desenvolvimento)
```bash
npm run api
```
- Servidor HTTP na porta 3000
- Endpoints para testes e integração

## 📡 API Endpoints

### Health Check
```bash
GET /health
curl http://localhost:3000/health
```

### Buscar Pacientes Recentes
```bash
GET /api/patients/recent
curl http://localhost:3000/api/patients/recent
```

### Informações de Configuração
```bash
GET /api/config
curl http://localhost:3000/api/config
```

## 🐳 Docker

```bash
# Construir e executar
docker-compose up nps-api

# Apenas construir
docker-compose build nps-api
```

## 📝 Logs

```bash
# Ver logs em tempo real
tail -f logs/combined.log

# Ver apenas erros
tail -f logs/error.log
```

## 📧 Configuração de Email (Resend)

### 1. Criar conta no Resend
1. Acesse [Resend.com](https://resend.com)
2. Crie uma conta gratuita (100 emails/dia)
3. Obtenha sua API Key

### 2. Configurar domínio (Opcional)
- Para usar email personalizado, configure seu domínio no Resend
- Caso contrário, use o domínio padrão do Resend

### 3. Relatório por Email
Após cada execução do job NPS, o sistema envia automaticamente:
- **Arquivo CSV**: Lista completa de pacientes com status de envio
- **Relatório HTML**: Resumo com métricas e estatísticas
- **Informações incluídas**:
  - Total de pacientes elegíveis
  - Mensagens enviadas com sucesso
  - Falhas no envio
  - Taxa de sucesso
  - Tempo de processamento

## 🚨 Troubleshooting

### Erro de Conexão IRIS
1. Verificar host, porta e credenciais
2. Testar conectividade: `ping $IRIS_HOST`
3. Verificar firewall e VPN

### Erro WhatsApp API
1. Verificar Access Token válido
2. Confirmar Phone Number ID
3. Verificar limites de rate limiting
4. Consultar logs para erros específicos

### Erro de Email
1. Verificar API Key do Resend
2. Confirmar email destinatário válido
3. Verificar logs para detalhes do erro
4. Testar envio manual via dashboard do Resend

## 📄 Licença

MIT License