import { EmailService } from './src/services/emailService.js';
import { getNewPatients } from './src/services/patientService.js';
import dotenv from 'dotenv';

// Carregar variáveis de ambiente
dotenv.config();

async function testEmailService() {
    console.log('🧪 Iniciando teste de envio de email com dados reais...\n');

    // Criar instância do serviço de email
    const emailService = new EmailService();

    try {
        console.log('🔍 Buscando pacientes do banco de dados...');
        
        // Buscar pacientes reais do banco
        const patients = await getNewPatients();
        
        if (patients.length === 0) {
            console.log('\n⚠️  Nenhum paciente elegível encontrado no banco de dados.');
            console.log('💡 Certifique-se de que existem pacientes dos últimos dias com telefone válido.');
            return;
        }

        console.log(`✅ Encontrados ${patients.length} pacientes elegíveis\n`);
        
        // Mostrar alguns detalhes dos pacientes encontrados
        console.log('📋 Primeiros 5 pacientes:');
        patients.slice(0, 5).forEach((patient, index) => {
            console.log(`${index + 1}. ${patient.nome} - ${patient.codigoOs} - Tel: ${patient.whatsapp}`);
        });
        
        // Preparar dados dos pacientes para o email (sem status de envio)
        const patientsForEmail = patients.map(patient => ({
            ...patient,
            name: patient.nome,
            phone: patient.whatsapp,
            visitDate: patient.data,
            messageSent: false, // Não foi enviada nenhuma mensagem
            error: 'Teste apenas de email - mensagem não enviada'
        }));

        // Estatísticas para o teste (todas as mensagens como não enviadas)
        const stats = {
            total: patients.length,
            sent: 0,
            failed: patients.length,
            duration: 0
        };

        console.log('\n📊 Dados para o relatório:');
        console.log(`- Total de pacientes: ${stats.total}`);
        console.log(`- Mensagens enviadas: ${stats.sent} (teste apenas de email)`);
        console.log(`- Não processadas: ${stats.failed}`);

        console.log('\n📧 Enviando email de teste...');
        console.log(`- De: ${process.env.EMAIL_FROM || '<EMAIL>'}`);
        console.log(`- Para: ${process.env.EMAIL_TO}`);
        console.log(`- API Key configurada: ${process.env.RESEND_API_KEY ? '✅' : '❌'}`);

        if (!process.env.RESEND_API_KEY) {
            throw new Error('RESEND_API_KEY não configurada no .env');
        }

        if (!process.env.EMAIL_TO) {
            throw new Error('EMAIL_TO não configurada no .env');
        }

        const result = await emailService.sendNpsReport(patientsForEmail, stats);
        
        console.log('\n✅ Email de teste enviado com sucesso!');
        console.log(`- ID do email: ${result.data?.id || 'N/A'}`);
        console.log('- Arquivo CSV anexado com dados reais dos pacientes');
        console.log('- Status de envio marcado como "não processado" para todos');
        console.log('\n📨 Verifique sua caixa de entrada!');

    } catch (error) {
        console.error('\n❌ Erro:', error.message);
        
        if (error.response?.data) {
            console.error('Detalhes do erro:', JSON.stringify(error.response.data, null, 2));
        }

        console.log('\n💡 Dicas:');
        console.log('1. Verifique se as variáveis de ambiente estão configuradas corretamente');
        console.log('2. Certifique-se de que o banco de dados está acessível');
        console.log('3. Verifique se existem pacientes elegíveis no banco (últimas 24h com telefone)');
        console.log('4. Confirme que EMAIL_TO contém um email válido');
        console.log('5. Verifique se RESEND_API_KEY está correta');
    }
}

// Executar teste
testEmailService().catch(console.error); 