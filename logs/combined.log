2025-06-22 19:36:16 [info] : Generating NPS report...
2025-06-22 19:36:16 [info] : CSV report generated: /Users/<USER>/Documents/Projects/adolfo-lutz-backend/temp/nps_report_2025-06-22T18-36-16-464Z.csv
2025-06-22 19:36:17 [info] : NPS report sent successfully {}
2025-06-22 19:39:28 [info] : Generating NPS report...
2025-06-22 19:39:28 [info] : CSV report generated: /Users/<USER>/Documents/Projects/adolfo-lutz-backend/temp/nps_report_2025-06-22T18-39-28-733Z.csv
2025-06-22 19:39:29 [info] : NPS report sent successfully {}
2025-06-22 19:41:06 [info] : Generating NPS report...
2025-06-22 19:41:06 [info] : CSV report generated: /Users/<USER>/Documents/Projects/adolfo-lutz-backend/temp/nps_report_2025-06-22T18-41-06-364Z.csv
2025-06-22 19:41:07 [info] : NPS report sent successfully {}
2025-06-24 14:30:01 [info] : Generating NPS report...
2025-06-24 14:30:01 [info] : CSV report generated: /Users/<USER>/Documents/Projects/adolfo-lutz-backend/temp/nps_report_2025-06-24T13-30-01-489Z.csv
2025-06-24 14:30:03 [info] : NPS report sent successfully {"emailId":"858c9097-f26b-442a-8d37-91bfebee8151"}
2025-06-24 14:35:53 [info] : Detected macOS, using macOS driver
2025-06-24 14:35:53 [info] : Using IRIS driver: /Users/<USER>/Documents/Projects/adolfo-lutz-backend/libirisodbc35.so (717389 bytes)
2025-06-24 14:35:53 [error] : Database connection failed: [odbc] Error connecting to the database {"odbcErrors":[{"state":"08S01","code":461,"message":"[Iris ODBC][State : 08S01][Native Code 461]\n[libirisodbc35.so]\nCommunication link failure"}]}
Error: [odbc] Error connecting to the database
2025-06-24 14:35:53 [error] : Error fetching patients: [odbc] Error connecting to the database {"odbcErrors":[{"state":"08S01","code":461,"message":"[Iris ODBC][State : 08S01][Native Code 461]\n[libirisodbc35.so]\nCommunication link failure"}]}
Error: [odbc] Error connecting to the database
2025-06-24 13:38:42 [info] : Detected Docker environment, using system Linux driver
2025-06-24 13:38:42 [info] : Using IRIS driver: /usr/lib/libirisodbc35.so (745840 bytes)
2025-06-24 13:38:42 [info] : NPS WhatsApp API Server running on port 3000
2025-06-24 13:38:42 [info] : Environment: development
2025-06-24 13:38:42 [info] : Available endpoints:
2025-06-24 13:38:42 [info] :   GET /health - Health check
2025-06-24 13:38:42 [info] :   GET /api/patients/recent - Get patients from last 24h
2025-06-24 13:38:42 [info] :   GET /api/config - Configuration info
2025-06-24 13:38:46 [info] : GET /health {"ip":"::ffff:127.0.0.1","userAgent":"curl/7.88.1"}
2025-06-24 14:40:12 [info] : Detected macOS, using macOS driver
2025-06-24 14:40:12 [info] : Using IRIS driver: /Users/<USER>/Documents/Projects/adolfo-lutz-backend/libirisodbc35.so (717389 bytes)
2025-06-24 14:40:12 [error] : Database connection failed: [odbc] Error connecting to the database {"odbcErrors":[{"state":"08S01","code":461,"message":"[Iris ODBC][State : 08S01][Native Code 461]\n[libirisodbc35.so]\nCommunication link failure"}]}
Error: [odbc] Error connecting to the database
2025-06-24 14:40:12 [error] : Error fetching patients: [odbc] Error connecting to the database {"odbcErrors":[{"state":"08S01","code":461,"message":"[Iris ODBC][State : 08S01][Native Code 461]\n[libirisodbc35.so]\nCommunication link failure"}]}
Error: [odbc] Error connecting to the database
2025-06-24 14:44:41 [info] : Database connection established
2025-06-24 14:44:42 [info] : Found 390 patients with valid WhatsApp numbers
2025-06-24 14:44:42 [info] : Database connection closed
2025-06-24 14:44:42 [info] : Generating NPS report...
2025-06-24 14:44:42 [info] : CSV report generated: /Users/<USER>/Documents/Projects/adolfo-lutz-backend/temp/nps_report_2025-06-24T13-44-42-322Z.csv
2025-06-24 14:44:44 [info] : NPS report sent successfully {"emailId":"1a5bf9a8-de1d-4178-b202-8f4e2ec2b87c"}
2025-06-24 14:46:05 [info] : Database connection established
2025-06-24 14:46:06 [info] : Found 390 patients with valid WhatsApp numbers
2025-06-24 14:46:06 [info] : Database connection closed
2025-06-24 14:46:06 [info] : Generating NPS report...
2025-06-24 14:46:06 [info] : CSV report generated: /Users/<USER>/Documents/Projects/adolfo-lutz-backend/temp/nps_report_2025-06-24T13-46-06-379Z.csv
2025-06-24 14:46:08 [info] : NPS report sent successfully {"emailId":"5c983a3e-bd8b-4029-9229-9fb9154c5f83"}
2025-06-24 13:52:03 [info] : Database connection established
2025-06-24 13:52:04 [info] : Found 390 patients with valid WhatsApp numbers
2025-06-24 13:52:04 [info] : Database connection closed
2025-06-24 13:52:04 [info] : Generating NPS report...
2025-06-24 13:52:04 [info] : CSV report generated: /app/temp/nps_report_2025-06-24T13-52-04-395Z.csv
2025-06-24 13:52:05 [info] : NPS report sent successfully {"emailId":"78958327-c814-4a3c-aa0a-3309d0f7dce7"}
2025-06-24 13:55:11 [info] : Database connection established
2025-06-24 13:55:12 [info] : Found 390 patients with valid WhatsApp numbers
2025-06-24 13:55:12 [info] : Database connection closed
2025-06-24 13:55:12 [info] : Generating NPS report...
2025-06-24 13:55:12 [info] : CSV report generated: /app/temp/nps_report_2025-06-24T13-55-12-429Z.csv
2025-06-24 13:55:13 [info] : NPS report sent successfully {"emailId":"38ce1047-67f4-4c8d-837f-23e1ce094a3d"}
2025-06-25 00:00:02 [error] : Failed to send WhatsApp to 351969174239: {"error":"Request failed with status code 401","status":401,"data":{"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"Ale1cjdq5Q8Z2fbjd_PGYg7"}},"patient":"TEST1750806002487"}
2025-06-25 00:00:02 [info] : Generating NPS report...
2025-06-25 00:00:02 [info] : CSV report generated: /Users/<USER>/Documents/Projects/adolfo-lutz-backend/temp/nps_report_2025-06-24T23-00-02-720Z.csv
2025-06-25 00:00:03 [info] : NPS report sent successfully {"emailId":"fa743f23-67c6-4e8a-847b-e610cdb25de9"}
2025-06-25 00:00:36 [error] : Failed to send WhatsApp to 351969174239: {"error":"Request failed with status code 401","status":401,"data":{"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"A9838os_ijuIakFd0zrxbpy"}},"patient":"TEST1750806036563"}
2025-06-25 00:00:36 [info] : Generating NPS report...
2025-06-25 00:00:36 [info] : CSV report generated: /Users/<USER>/Documents/Projects/adolfo-lutz-backend/temp/nps_report_2025-06-24T23-00-36-766Z.csv
2025-06-25 00:00:37 [info] : NPS report sent successfully {"emailId":"309beba3-a343-433d-a4b1-afcccc8f1452"}
2025-06-25 00:12:14 [error] : Failed to send WhatsApp to 351969174239: {"error":"Request failed with status code 401","status":401,"data":{"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AuGH7JyAkbYSBdtZh2bSPgO"}},"patient":"TEST1750806734445"}
2025-06-25 00:12:14 [info] : Generating NPS report...
2025-06-25 00:12:14 [info] : CSV report generated: /Users/<USER>/Documents/Projects/adolfo-lutz-backend/temp/nps_report_2025-06-24T23-12-14-672Z.csv
2025-06-25 00:12:15 [info] : NPS report sent successfully {"emailId":"0ea0d055-a3d5-42bd-81e3-b4ce47e74b23"}
2025-06-25 00:13:14 [error] : Failed to send WhatsApp to 351969174239: {"error":"Request failed with status code 400","status":400,"data":{"error":{"message":"Unsupported post request. Object with ID '558132642926' does not exist, cannot be loaded due to missing permissions, or does not support this operation. Please read the Graph API documentation at https://developers.facebook.com/docs/graph-api","type":"GraphMethodException","code":100,"error_subcode":33,"fbtrace_id":"AY7vG5g1J82BY_6OCIvCSFF"}},"patient":"TEST1750806793411"}
2025-06-25 00:13:14 [info] : Generating NPS report...
2025-06-25 00:13:14 [info] : CSV report generated: /Users/<USER>/Documents/Projects/adolfo-lutz-backend/temp/nps_report_2025-06-24T23-13-14-003Z.csv
2025-06-25 00:13:14 [info] : NPS report sent successfully {"emailId":"e775fa0b-23aa-4c82-b14f-c4bbc1529141"}
2025-06-25 00:14:17 [info] : Generating NPS report...
2025-06-25 00:14:17 [info] : CSV report generated: /Users/<USER>/Documents/Projects/adolfo-lutz-backend/temp/nps_report_2025-06-24T23-14-17-562Z.csv
2025-06-25 00:14:18 [info] : NPS report sent successfully {"emailId":"603b170b-ac3b-475c-8443-e6149aa02b92"}
