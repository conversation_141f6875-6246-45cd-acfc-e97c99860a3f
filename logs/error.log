2025-06-24 14:35:53 [error] : Database connection failed: [odbc] Error connecting to the database {"odbcErrors":[{"state":"08S01","code":461,"message":"[Iris ODBC][State : 08S01][Native Code 461]\n[libirisodbc35.so]\nCommunication link failure"}]}
Error: [odbc] <PERSON><PERSON>r connecting to the database
2025-06-24 14:35:53 [error] : <PERSON>rror fetching patients: [odbc] Error connecting to the database {"odbcErrors":[{"state":"08S01","code":461,"message":"[Iris ODBC][State : 08S01][Native Code 461]\n[libirisodbc35.so]\nCommunication link failure"}]}
Error: [odbc] Error connecting to the database
2025-06-24 14:40:12 [error] : Database connection failed: [odbc] Error connecting to the database {"odbcErrors":[{"state":"08S01","code":461,"message":"[<PERSON> ODBC][State : 08S01][Native Code 461]\n[libirisodbc35.so]\nCommunication link failure"}]}
Error: [odbc] Error connecting to the database
2025-06-24 14:40:12 [error] : Error fetching patients: [odbc] Error connecting to the database {"odbcErrors":[{"state":"08S01","code":461,"message":"[Iris ODBC][State : 08S01][Native Code 461]\n[libirisodbc35.so]\nCommunication link failure"}]}
Error: [odbc] Error connecting to the database
2025-06-25 00:00:02 [error] : Failed to send WhatsApp to 351969174239: {"error":"Request failed with status code 401","status":401,"data":{"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"Ale1cjdq5Q8Z2fbjd_PGYg7"}},"patient":"TEST1750806002487"}
2025-06-25 00:00:36 [error] : Failed to send WhatsApp to 351969174239: {"error":"Request failed with status code 401","status":401,"data":{"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"A9838os_ijuIakFd0zrxbpy"}},"patient":"TEST1750806036563"}
2025-06-25 00:12:14 [error] : Failed to send WhatsApp to 351969174239: {"error":"Request failed with status code 401","status":401,"data":{"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AuGH7JyAkbYSBdtZh2bSPgO"}},"patient":"TEST1750806734445"}
2025-06-25 00:13:14 [error] : Failed to send WhatsApp to 351969174239: {"error":"Request failed with status code 400","status":400,"data":{"error":{"message":"Unsupported post request. Object with ID '558132642926' does not exist, cannot be loaded due to missing permissions, or does not support this operation. Please read the Graph API documentation at https://developers.facebook.com/docs/graph-api","type":"GraphMethodException","code":100,"error_subcode":33,"fbtrace_id":"AY7vG5g1J82BY_6OCIvCSFF"}},"patient":"TEST1750806793411"}
