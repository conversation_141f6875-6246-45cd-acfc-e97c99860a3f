# 📱 Guia da API do WhatsApp Business

Documentação completa para configurar e usar a API do WhatsApp Business com o NPS WhatsApp Service.

## 🎯 Visão Geral

Este serviço utiliza a **WhatsApp Business API** oficial do Meta (Facebook) para enviar mensagens de pesquisa NPS automaticamente para pacientes de laboratório.

## 📋 Pré-requisitos

### 1. Conta Facebook Business
- Conta Facebook Business ativa
- Acesso ao Facebook Business Manager
- Verificação de identidade completa

### 2. WhatsApp Business Account
- Número de telefone comercial dedicado
- WhatsApp Business Account verificado
- Acesso ao WhatsApp Business API

## 🚀 Configuração Passo a Passo

### Passo 1: Criar Aplicação no Facebook Developers

1. **Acesse**: [Facebook Developers](https://developers.facebook.com)
2. **Crie uma nova aplicação**:
   - Tipo: "Business"
   - Nome: "NPS WhatsApp Service"
   - Email de contato: <EMAIL>

3. **Adicione o produto WhatsApp**:
   - No painel da aplicação
   - Clique em "Adicionar Produto"
   - Selecione "WhatsApp Business API"

### Passo 2: Configurar WhatsApp Business API

1. **Obter Access Token**:
   ```
   Painel WhatsApp > Configuração > Access Token
   Copie o token temporário (24h) ou gere um permanente
   ```

2. **Obter Phone Number ID**:
   ```
   Painel WhatsApp > Configuração > Números de Telefone
   Copie o Phone Number ID do número verificado
   ```

3. **Configurar Webhook (Opcional)**:
   ```
   URL: https://seu-dominio.com/webhook
   Verify Token: seu-token-verificacao
   ```

### Passo 3: Configurar Variáveis de Ambiente

```bash
# WhatsApp Business API
WHATSAPP_ACCESS_TOKEN=EAAxxxxxxxxxxxxxxx
WHATSAPP_PHONE_NUMBER_ID=123456789012345
NPS_FORM_URL=https://forms.laboratorio.com.br/nps

# Webhook (opcional)
WHATSAPP_WEBHOOK_URL=https://seu-dominio.com/webhook
WHATSAPP_VERIFY_TOKEN=meu-token-verificacao
```

## 🔧 Configuração Avançada

### Access Token Permanente

Para produção, configure um access token permanente:

1. **No Facebook Business Manager**:
   - Vá para "Configurações do Sistema"
   - Selecione "Tokens de Acesso"
   - Gere um token permanente

2. **Configurar no .env**:
   ```bash
   WHATSAPP_ACCESS_TOKEN=seu-token-permanente
   ```

### Webhook para Confirmações

Configure webhook para receber confirmações de entrega:

1. **URL do Webhook**:
   ```
   https://seu-dominio.com/webhook
   ```

2. **Eventos Subscritos**:
   - `messages` - Mensagens recebidas
   - `message_deliveries` - Confirmações de entrega
   - `message_reads` - Confirmações de leitura

## 📱 Formato das Mensagens

### Mensagem NPS Padrão

```
🏥 Pesquisa de Satisfação

Olá [NOME]!

Esperamos que você tenha tido uma boa experiência em nosso laboratório.

Gostaríamos de saber sua opinião sobre o atendimento que você recebeu (OS: [CODIGO_OS]).

Em uma escala de 0 a 10, o quanto você recomendaria nosso laboratório para um amigo ou familiar?

👉 [Link para formulário NPS]

Sua opinião é muito importante para nós! 💙
```

### Personalização da Mensagem

Edite o arquivo `src/services/whatsappService.js`:

```javascript
function createNPSMessage(patient, formUrl) {
    return `🏥 Pesquisa de Satisfação

Olá ${patient.nome}!

Esperamos que você tenha tido uma boa experiência em nosso laboratório.

Gostaríamos de saber sua opinião sobre o atendimento que você recebeu (OS: ${patient.codigoOs}).

Em uma escala de 0 a 10, o quanto você recomendaria nosso laboratório para um amigo ou familiar?

👉 ${formUrl}

Sua opinião é muito importante para nós! 💙`;
}
```

## 🔍 Testando a Configuração

### 1. Testar Configuração
```bash
npm run api
curl http://localhost:3000/api/config
```

**Resposta esperada**:
```json
{
  "whatsapp": {
    "configured": true,
    "phoneNumberId": "configured"
  }
}
```

### 2. Testar Busca de Pacientes
```bash
curl http://localhost:3000/api/patients/recent
```

### 3. Teste Manual de Envio
```bash
# Executar job manualmente
npm run api
# Acessar endpoint de teste (se implementado)
```

## 📊 Monitoramento e Logs

### Logs de WhatsApp

```bash
# Ver logs específicos do WhatsApp
tail -f logs/combined.log | grep "WhatsApp"

# Ver erros de envio
tail -f logs/error.log | grep "WhatsApp"
```

### Métricas Importantes

- **Mensagens enviadas**: Total de mensagens processadas
- **Mensagens entregues**: Confirmações de entrega
- **Mensagens lidas**: Confirmações de leitura
- **Erros de envio**: Falhas na API

## 🚨 Troubleshooting

### Erro: "Invalid Access Token"
```
Solução:
1. Verificar se o token não expirou
2. Gerar novo token no Facebook Developers
3. Atualizar variável WHATSAPP_ACCESS_TOKEN
```

### Erro: "Phone Number Not Found"
```
Solução:
1. Verificar Phone Number ID
2. Confirmar que o número está verificado
3. Verificar permissões da aplicação
```

### Erro: "Rate Limit Exceeded"
```
Solução:
1. Reduzir frequência de envio
2. Implementar retry com backoff
3. Verificar limites da conta Business
```

### Erro: "Message Template Not Approved"
```
Solução:
1. Usar apenas mensagens de texto simples
2. Evitar templates não aprovados
3. Submeter templates para aprovação
```

## 🔒 Segurança e Compliance

### Proteção de Dados
- **LGPD**: Dados pessoais são mascarados nos logs
- **Criptografia**: Tokens armazenados como variáveis de ambiente
- **Auditoria**: Logs detalhados de todas as operações

### Boas Práticas
1. **Nunca** commitar tokens no código
2. **Sempre** usar HTTPS para webhooks
3. **Implementar** rate limiting
4. **Monitorar** logs de segurança

## 📈 Limites e Quotas

### Limites da API WhatsApp Business

| Tipo | Limite | Observações |
|------|--------|-------------|
| Mensagens/dia | 1.000 - 100.000+ | Baseado no tier da conta |
| Rate Limit | 80 msg/segundo | Para contas verificadas |
| Template Messages | Ilimitado | Após aprovação |
| Session Messages | 24h window | Resposta a mensagens recebidas |

### Otimização
- **Batch Processing**: Agrupar envios
- **Queue System**: Implementar fila de mensagens
- **Retry Logic**: Tentar novamente em caso de erro

## 🎯 Próximos Passos

1. **Configurar** webhook para confirmações
2. **Implementar** templates de mensagem
3. **Adicionar** métricas de entrega
4. **Configurar** alertas de erro
5. **Implementar** dashboard de monitoramento

## 📞 Suporte

### Recursos Oficiais
- [WhatsApp Business API Docs](https://developers.facebook.com/docs/whatsapp)
- [Facebook Business Help](https://business.facebook.com/help)
- [WhatsApp Business API Changelog](https://developers.facebook.com/docs/whatsapp/changelog)

### Logs e Debug
```bash
# Ativar debug mode
DEBUG=whatsapp npm run api

# Ver logs detalhados
tail -f logs/combined.log | grep -E "(WhatsApp|API|Error)"
```

**A API do WhatsApp está pronta para uso! 🚀**
